-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Mar 02, 2025 at 07:57 PM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `air_way_bill`
--

-- --------------------------------------------------------

--
-- Table structure for table `accounts_configs`
--

CREATE TABLE `accounts_configs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `Vat` varchar(255) NOT NULL,
  `UsdRate` varchar(255) NOT NULL,
  `PoundRate` varchar(255) NOT NULL,
  `EuroRate` varchar(255) NOT NULL,
  `ForkliftHourlyRate` varchar(255) NOT NULL,
  `CreatedBy` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `accounts_configs`
--

INSERT INTO `accounts_configs` (`id`, `Vat`, `UsdRate`, `PoundRate`, `EuroRate`, `ForkliftHourlyRate`, `CreatedBy`, `created_at`, `updated_at`) VALUES
(1, '1.16', '1200', '1100', '1000', '20000', '1', '2025-02-26 20:24:57', '2025-02-26 20:24:57'),
(2, '16.5', '2000', '2111', '3333', '200', '1', '2025-02-26 20:46:50', '2025-02-26 20:46:50'),
(3, '12', '122', '112', '212', '2321', '1', '2025-02-26 21:12:53', '2025-02-26 21:12:53'),
(4, '0.912', '2000', '1000', '1000', '20000', '1', '2025-02-28 06:50:07', '2025-02-28 06:50:07');

-- --------------------------------------------------------

--
-- Table structure for table `agents`
--

CREATE TABLE `agents` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `AgentName` varchar(255) NOT NULL,
  `AgentAccountNo` varchar(255) NOT NULL,
  `AgentAddress1` varchar(255) DEFAULT NULL,
  `AgentAddress2` varchar(255) DEFAULT NULL,
  `AgentAddress3` varchar(255) DEFAULT NULL,
  `AgentEmail` varchar(255) NOT NULL,
  `AgentPhonenumber` varchar(255) DEFAULT NULL,
  `AgentType` varchar(255) NOT NULL,
  `AccountState` varchar(255) DEFAULT NULL,
  `TaxType` varchar(255) NOT NULL,
  `ContactName` varchar(255) DEFAULT NULL,
  `ContactPhone` varchar(255) DEFAULT NULL,
  `ContactFax` varchar(255) DEFAULT NULL,
  `ContactEmail` varchar(255) DEFAULT NULL,
  `PaymentType` varchar(255) NOT NULL,
  `CreatedBy` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `agents`
--

INSERT INTO `agents` (`id`, `AgentName`, `AgentAccountNo`, `AgentAddress1`, `AgentAddress2`, `AgentAddress3`, `AgentEmail`, `AgentPhonenumber`, `AgentType`, `AccountState`, `TaxType`, `ContactName`, `ContactPhone`, `ContactFax`, `ContactEmail`, `PaymentType`, `CreatedBy`, `created_at`, `updated_at`) VALUES
(1, 'mark', 'Emirates', 'Po Box12', 'Blantyre', 'Malawi', '<EMAIL>', NULL, '1', NULL, '2', 'sda', '32', NULL, NULL, '1', NULL, '2025-02-25 05:23:57', '2025-02-25 05:23:57'),
(2, 'asdasd', 'asdasd', 'sdasda', 'sdasas', 'sdas', '<EMAIL>', NULL, '1', NULL, '2', NULL, NULL, NULL, NULL, '1', NULL, '2025-02-25 05:28:44', '2025-02-25 05:28:44'),
(3, 'asdasd', 'asdasd', 'sdasda', 'sdasas', 'sdas', '<EMAIL>', 'asda', '1', NULL, '2', NULL, NULL, NULL, NULL, '1', NULL, '2025-02-25 05:45:28', '2025-02-25 05:45:28');

-- --------------------------------------------------------

--
-- Table structure for table `airlines`
--

CREATE TABLE `airlines` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `AirlineCode` varchar(255) NOT NULL,
  `AirlineName` varchar(255) NOT NULL,
  `AirlinePrefix` varchar(255) NOT NULL,
  `AirlineFrequency` varchar(255) NOT NULL,
  `CreatedBy` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `airlines`
--

INSERT INTO `airlines` (`id`, `AirlineCode`, `AirlineName`, `AirlinePrefix`, `AirlineFrequency`, `CreatedBy`, `created_at`, `updated_at`) VALUES
(1, 'mm2', 'mark', 'mrk3', '1', NULL, '2025-02-23 20:25:18', '2025-02-26 06:07:01'),
(3, '12034', 'Emirates MM', 'KA09', '1', NULL, '2025-02-23 20:34:21', '2025-02-25 02:25:56'),
(20, 'ETH1', 'Ethiopian', 'ETH', 'admin', NULL, '2025-02-23 20:32:29', '2025-02-23 20:32:29'),
(21, '134', 'Emirates MM3', 'K34', '1', NULL, '2025-02-26 02:24:14', '2025-02-26 02:24:14'),
(22, '13p0', 'Emirates MM0', 'K34p0', '0', NULL, '2025-02-26 07:39:08', '2025-02-26 07:39:08');

-- --------------------------------------------------------

--
-- Table structure for table `airports`
--

CREATE TABLE `airports` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `AirportCode` varchar(255) NOT NULL,
  `AirportName` varchar(255) NOT NULL,
  `AirportCountry` varchar(255) NOT NULL,
  `Frequency` varchar(255) NOT NULL,
  `CreatedBy` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `airports`
--

INSERT INTO `airports` (`id`, `AirportCode`, `AirportName`, `AirportCountry`, `Frequency`, `CreatedBy`, `created_at`, `updated_at`) VALUES
(1, 'BT2', 'Baluti', 'UNITED ARAB EMIRATES', '1', NULL, '2025-02-23 21:50:22', '2025-02-28 03:13:35'),
(2, '898', 'sdfslkdjflskd', 'ANDORRA', '0', NULL, '2025-02-26 05:52:10', '2025-02-26 05:52:10'),
(3, 'LLW', 'Kamuzu International Airport', 'MALAWI', '1', NULL, '2025-02-26 12:41:29', '2025-02-26 12:41:29'),
(4, 'BLZ', 'Chileka', 'MALAWI', '1', NULL, '2025-02-26 12:42:24', '2025-02-26 12:42:24');

-- --------------------------------------------------------

--
-- Table structure for table `awbs`
--

CREATE TABLE `awbs` (
  `id` int(11) NOT NULL,
  `SerialNo` varchar(1000) DEFAULT '0',
  `AwbNo` varchar(50) DEFAULT NULL,
  `HAWB` varchar(50) NOT NULL DEFAULT '-',
  `ULD` int(11) NOT NULL DEFAULT 0,
  `ManifestID` int(11) NOT NULL DEFAULT 0,
  `TotalPieces` varchar(50) DEFAULT NULL,
  `ToCome` varchar(50) DEFAULT NULL,
  `TotalGrossWgtVol` varchar(50) DEFAULT NULL,
  `ToComeGrossWgtVol` varchar(50) DEFAULT NULL,
  `CargoDescription` varchar(50) DEFAULT NULL,
  `PointOfOrg` varchar(50) DEFAULT NULL,
  `PointOfDest` varchar(50) DEFAULT NULL,
  `NatureOfGoods` varchar(50) DEFAULT NULL,
  `Comment` varchar(500) DEFAULT NULL,
  `OnHandWgtVol` varchar(11) NOT NULL DEFAULT '0',
  `OnHandPieces` int(11) NOT NULL DEFAULT 0,
  `ChargableWgt` varchar(11) NOT NULL DEFAULT '0',
  `Charge` decimal(10,2) NOT NULL DEFAULT 0.00,
  `PaymentMethod` int(11) NOT NULL DEFAULT 0,
  `InvoiceNo` varchar(100) NOT NULL DEFAULT '-',
  `ChequeNo` varchar(100) NOT NULL DEFAULT '-',
  `PaymentStatus` int(11) NOT NULL DEFAULT 0,
  `CustomsNo` varchar(50) NOT NULL DEFAULT '-',
  `CheckedInStatus` int(11) NOT NULL DEFAULT 0,
  `CheckedInDate` datetime DEFAULT NULL,
  `ValidationCode` varchar(50) NOT NULL DEFAULT '-',
  `TimeStamp` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `DocValidationNo` varchar(16) NOT NULL DEFAULT '-',
  `CheckoutStatus` int(11) NOT NULL DEFAULT 0,
  `ReleaseStatus` int(11) NOT NULL DEFAULT 0,
  `IsPostOfficeCargo` int(11) NOT NULL DEFAULT 0,
  `MraValidated` int(11) NOT NULL DEFAULT 0,
  `IsConsolidated` int(11) NOT NULL DEFAULT 0,
  `IsPartOfConsolidatedAwbID` int(11) NOT NULL DEFAULT 0,
  `IsPartShipmentMain` int(11) DEFAULT 0,
  `IsPartShipmentOf` varchar(100) DEFAULT '0',
  `FileLink` varchar(200) NOT NULL DEFAULT '0',
  `CargoState1Remarks` varchar(500) DEFAULT NULL,
  `CargoState2Remarks` varchar(500) DEFAULT NULL,
  `CargoState3Remarks` varchar(500) DEFAULT NULL,
  `IsCompletelyMissing` int(11) NOT NULL DEFAULT 0,
  `PaymentDate` date NOT NULL DEFAULT current_timestamp(),
  `BayNumber` int(11) DEFAULT NULL,
  `IsDeleted` int(11) DEFAULT 0,
  `generatePartshipmentNote` int(11) DEFAULT 0,
  `numberOfPartShipments` int(11) NOT NULL DEFAULT 0,
  `deadSerialNo` varchar(250) DEFAULT NULL,
  `ReleaseDate` timestamp NOT NULL DEFAULT current_timestamp(),
  `CheckOutDate` timestamp NOT NULL DEFAULT current_timestamp(),
  `CheckOutUser` int(11) DEFAULT NULL,
  `ReleaseUser` int(11) DEFAULT NULL,
  `CheckInUser` int(11) DEFAULT NULL,
  `CargoTypeCheckIn` int(11) DEFAULT NULL,
  `CargoTypeCheckInUser` int(11) DEFAULT NULL,
  `whoProcessedThePayment` int(11) DEFAULT NULL,
  `receiptNumber` int(11) DEFAULT NULL,
  `cargoTypeCheckInDate` timestamp NOT NULL DEFAULT current_timestamp(),
  `isFree` int(11) DEFAULT 0,
  `hasPartShipments` int(11) DEFAULT 0,
  `isExcess` int(11) DEFAULT 0,
  `AF1Isprinted` int(11) DEFAULT 0,
  `MRADocIsprinted` int(11) DEFAULT 0,
  `ConsigneeDocIsprinted` int(11) DEFAULT 0,
  `receiptIsPrinted` int(11) DEFAULT 0,
  `partShipmentNoteIsPrinted` int(11) DEFAULT 0,
  `partShipmentReceiptIsPrinted` int(11) DEFAULT 0,
  `dontGoBack` int(11) DEFAULT 0,
  `partOfMain` int(11) DEFAULT 0,
  `created_at` timestamp(6) NOT NULL DEFAULT current_timestamp(6),
  `updated_at` timestamp(6) NOT NULL DEFAULT current_timestamp(6)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `awbs`
--

INSERT INTO `awbs` (`id`, `SerialNo`, `AwbNo`, `HAWB`, `ULD`, `ManifestID`, `TotalPieces`, `ToCome`, `TotalGrossWgtVol`, `ToComeGrossWgtVol`, `CargoDescription`, `PointOfOrg`, `PointOfDest`, `NatureOfGoods`, `Comment`, `OnHandWgtVol`, `OnHandPieces`, `ChargableWgt`, `Charge`, `PaymentMethod`, `InvoiceNo`, `ChequeNo`, `PaymentStatus`, `CustomsNo`, `CheckedInStatus`, `CheckedInDate`, `ValidationCode`, `TimeStamp`, `DocValidationNo`, `CheckoutStatus`, `ReleaseStatus`, `IsPostOfficeCargo`, `MraValidated`, `IsConsolidated`, `IsPartOfConsolidatedAwbID`, `IsPartShipmentMain`, `IsPartShipmentOf`, `FileLink`, `CargoState1Remarks`, `CargoState2Remarks`, `CargoState3Remarks`, `IsCompletelyMissing`, `PaymentDate`, `BayNumber`, `IsDeleted`, `generatePartshipmentNote`, `numberOfPartShipments`, `deadSerialNo`, `ReleaseDate`, `CheckOutDate`, `CheckOutUser`, `ReleaseUser`, `CheckInUser`, `CargoTypeCheckIn`, `CargoTypeCheckInUser`, `whoProcessedThePayment`, `receiptNumber`, `cargoTypeCheckInDate`, `isFree`, `hasPartShipments`, `isExcess`, `AF1Isprinted`, `MRADocIsprinted`, `ConsigneeDocIsprinted`, `receiptIsPrinted`, `partShipmentNoteIsPrinted`, `partShipmentReceiptIsPrinted`, `dontGoBack`, `partOfMain`, `created_at`, `updated_at`) VALUES
(16, '-', '5434', '-', 0, 2, '908', NULL, '453', NULL, 'twtw', 'BT2', 'LLW', 'hads', NULL, '0', 0, '0', 0.00, 0, '-', '-', 0, '-', 0, NULL, '-', '2025-02-28 09:31:06', '-', 0, 0, 0, 0, 1, 0, 0, '0', '0', NULL, NULL, NULL, 0, '2025-02-27', NULL, 0, 0, 0, NULL, '2025-02-27 18:36:10', '2025-02-27 18:36:10', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-02-27 18:36:10', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '2025-02-27 16:36:10.000000', '2025-02-28 07:31:06.000000'),
(17, '0', '5434', '9088', 0, 2, '95', NULL, '345', NULL, 'isidisd', 'b1', 'LLW', 'ncnx', NULL, '0', 0, '0', 0.00, 0, '-', '-', 0, '-', 0, NULL, '-', '2025-02-27 18:36:44', '-', 0, 0, 0, 0, 0, 16, 0, '0', '0', NULL, NULL, NULL, 0, '2025-02-27', NULL, 0, 0, 0, NULL, '2025-02-27 18:36:44', '2025-02-27 18:36:44', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-02-27 18:36:44', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '2025-02-27 16:36:44.000000', '2025-02-27 16:36:44.000000'),
(18, '-', '565', '-', 0, 2, '988', NULL, '445', NULL, 'cvv', 'b1', 'LLW', 'fddffd', NULL, '0', 0, '0', 0.00, 0, '-', '-', 0, '-', 0, NULL, '-', '2025-02-27 20:17:25', '-', 0, 0, 0, 0, 1, 0, 0, '0', '0', NULL, NULL, NULL, 0, '2025-02-27', NULL, 0, 0, 0, NULL, '2025-02-27 20:17:25', '2025-02-27 20:17:25', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-02-27 20:17:25', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '2025-02-27 18:17:25.000000', '2025-02-27 18:17:25.000000'),
(19, '0', '5434', '1222', 0, 2, '90', NULL, '90', NULL, 'mm', 'b1', 'BLZ', 'mm', NULL, '0', 0, '0', 0.00, 0, '-', '-', 0, '-', 0, NULL, '-', '2025-02-28 09:31:19', '-', 0, 0, 0, 0, 1, 16, 0, '0', '0', NULL, NULL, NULL, 0, '2025-02-27', NULL, 0, 0, 0, NULL, '2025-02-27 20:20:06', '2025-02-27 20:20:06', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-02-27 20:20:06', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '2025-02-27 18:20:06.000000', '2025-02-28 07:31:19.000000'),
(20, '-', '111111111111', '-', 0, 2, '*********', NULL, '445', NULL, 'cvv', '898', 'BLZ', 'mmmm', NULL, '0', 0, '0', 0.00, 0, '-', '-', 0, '-', 0, NULL, '-', '2025-02-28 09:29:44', '-', 0, 0, 0, 0, 1, 0, 0, '0', '0', NULL, NULL, NULL, 0, '2025-02-27', NULL, 0, 0, 0, NULL, '2025-02-27 20:22:38', '2025-02-27 20:22:38', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-02-27 20:22:38', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '2025-02-27 18:22:38.000000', '2025-02-28 07:29:44.000000'),
(21, '0', '0000000000', '-', 0, 2, '20000', NULL, '2000', NULL, 'Air Mail', 'BT2', 'LLW', 'Mail', NULL, '0', 0, '0', 0.00, 0, '-', '-', 0, '-', 0, NULL, '-', '2025-02-28 11:15:55', '-', 0, 0, 1, 0, 0, 16, 0, '0', '0', NULL, NULL, NULL, 0, '2025-02-28', NULL, 0, 0, 0, NULL, '2025-02-28 11:15:55', '2025-02-28 11:15:55', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-02-28 11:15:55', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '2025-02-28 09:15:55.000000', '2025-02-28 09:15:55.000000'),
(22, '-', '888-999', '-', 0, 5, '190', NULL, '1232', NULL, 'please', 'BT2', 'LLW', '<EMAIL>', NULL, '0', 0, '0', 0.00, 0, '-', '-', 0, '-', 0, NULL, '-', '2025-03-01 23:06:35', '-', 0, 0, 0, 0, 0, 0, 0, '0', '0', NULL, NULL, NULL, 0, '2025-03-02', NULL, 0, 0, 0, NULL, '2025-03-01 23:06:35', '2025-03-01 23:06:35', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-03-01 23:06:35', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, '2025-03-01 21:06:35.000000', '2025-03-01 21:06:35.000000');

-- --------------------------------------------------------

--
-- Table structure for table `awb_action_audit`
--

CREATE TABLE `awb_action_audit` (
  `id` int(11) NOT NULL,
  `action` varchar(250) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `awb_action_audit`
--

INSERT INTO `awb_action_audit` (`id`, `action`) VALUES
(3, 'ASSIGN'),
(6, 'CEHCK-OUT'),
(2, 'CHECK-IN'),
(1, 'CREATE'),
(4, 'NOTIFY'),
(5, 'PAY'),
(7, 'RELEASE'),
(8, 'VALIDATE');

-- --------------------------------------------------------

--
-- Table structure for table `awb_audit_table`
--

CREATE TABLE `awb_audit_table` (
  `id` int(11) NOT NULL,
  `awbID` int(11) NOT NULL,
  `typeOfAwb` int(11) NOT NULL,
  `UserID` int(11) NOT NULL,
  `action` int(11) NOT NULL,
  `Desciption` varchar(500) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `awb_cargo_states`
--

CREATE TABLE `awb_cargo_states` (
  `id` int(11) NOT NULL,
  `AwbID` int(11) NOT NULL,
  `CargoState0` int(11) DEFAULT 0,
  `CargoState0Pieces` int(11) DEFAULT 0,
  `CargoState0Weight` varchar(50) DEFAULT '0',
  `CargoState1` int(11) DEFAULT 0,
  `CargoState1Pieces` int(11) DEFAULT 0,
  `CargoState1Weight` varchar(50) DEFAULT '0',
  `CargoState2` int(11) DEFAULT 0,
  `CargoState2Pieces` int(11) DEFAULT 0,
  `CargoState2Weight` varchar(50) DEFAULT '0',
  `CargoState3` int(11) DEFAULT 0,
  `CargoState3Pieces` int(11) DEFAULT 0,
  `CargoState3Weight` varchar(50) DEFAULT '0',
  `Timestamp` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `awb_cargo_states`
--

INSERT INTO `awb_cargo_states` (`id`, `AwbID`, `CargoState0`, `CargoState0Pieces`, `CargoState0Weight`, `CargoState1`, `CargoState1Pieces`, `CargoState1Weight`, `CargoState2`, `CargoState2Pieces`, `CargoState2Weight`, `CargoState3`, `CargoState3Pieces`, `CargoState3Weight`, `Timestamp`) VALUES
(1, 1, 1, 0, '0', NULL, 0, '0', NULL, 0, '0', NULL, 0, '0', '2025-02-18 07:33:03'),
(2, 2, 1, 0, '0', NULL, 0, '0', NULL, 0, '0', NULL, 0, '0', '2025-02-18 11:42:00'),
(3, 3, 1, 0, '0', NULL, 0, '0', NULL, 0, '0', NULL, 0, '0', '2025-02-18 11:49:22'),
(4, 4, 4, 2, '20', NULL, 0, '0', NULL, 0, '0', NULL, 0, '0', '2025-02-18 12:24:07'),
(5, 5, 1, 0, '0', NULL, 0, '0', NULL, 0, '0', NULL, 0, '0', '2025-02-27 01:16:02'),
(6, 6, 0, 0, '0', 0, 0, '0', 0, 0, '0', 0, 0, '0', '2025-02-27 07:41:15'),
(7, 7, 0, 0, '0', 0, 0, '0', 0, 0, '0', 0, 0, '0', '2025-02-27 09:16:00'),
(8, 8, 0, 0, '0', 0, 0, '0', 0, 0, '0', 0, 0, '0', '2025-02-27 13:13:40'),
(9, 9, 0, 0, '0', 0, 0, '0', 0, 0, '0', 0, 0, '0', '2025-02-27 16:23:04'),
(10, 10, 0, 0, '0', 0, 0, '0', 0, 0, '0', 0, 0, '0', '2025-02-27 17:55:45'),
(11, 11, 0, 0, '0', 0, 0, '0', 0, 0, '0', 0, 0, '0', '2025-02-27 17:56:23'),
(12, 12, 0, 0, '0', 0, 0, '0', 0, 0, '0', 0, 0, '0', '2025-02-27 20:21:14'),
(13, 13, 1, 1000, '1500', NULL, 0, '0', NULL, 0, '0', NULL, 0, '0', '2025-02-28 10:44:17'),
(14, 14, 6, 0, '0', 0, 0, '0', 0, 0, '0', 0, 0, '0', '2025-02-28 11:30:11'),
(15, 15, 0, 0, '0', 0, 0, '0', 0, 0, '0', 0, 0, '0', '2025-02-28 11:59:10'),
(16, 16, NULL, 0, '0', NULL, 0, '0', NULL, 0, '0', NULL, 0, '0', '2025-02-28 13:18:19');

-- --------------------------------------------------------

--
-- Table structure for table `awb_cargo_types`
--

CREATE TABLE `awb_cargo_types` (
  `id` int(11) NOT NULL,
  `AwbID` int(11) DEFAULT NULL,
  `CargoType1` int(11) NOT NULL DEFAULT 0,
  `CargoType1Weight` varchar(50) DEFAULT '0',
  `CargoType2` int(11) NOT NULL DEFAULT 0,
  `CargoType2Weight` varchar(50) DEFAULT '0',
  `CargoType3` int(11) NOT NULL DEFAULT 0,
  `CargoType3Weight` varchar(50) DEFAULT '0',
  `CargoType4` int(11) NOT NULL DEFAULT 0,
  `CargoType4Weight` varchar(50) DEFAULT '0',
  `Timestamp` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `awb_cargo_types`
--

INSERT INTO `awb_cargo_types` (`id`, `AwbID`, `CargoType1`, `CargoType1Weight`, `CargoType2`, `CargoType2Weight`, `CargoType3`, `CargoType3Weight`, `CargoType4`, `CargoType4Weight`, `Timestamp`) VALUES
(1, 1, 1, '500.0', 0, '0', 0, '0', 0, '0', '2025-02-18 11:45:00'),
(2, 2, 15, '200.0', 0, '0', 0, '0', 0, '0', '2025-02-18 11:45:23'),
(3, 3, 1, '100.0', 0, '0', 0, '0', 0, '0', '2025-02-18 11:49:58'),
(4, 4, 1, '200', 0, '0', 0, '0', 0, '0', '2025-02-18 12:26:21'),
(5, 5, 0, '0', 0, '0', 0, '0', 0, '0', '2025-02-27 02:14:21'),
(6, 6, 0, '0', 0, '0', 0, '0', 0, '0', '2025-02-27 07:41:15'),
(7, 7, 0, '0', 0, '0', 0, '0', 0, '0', '2025-02-27 09:16:00'),
(8, 8, 0, '0', 0, '0', 0, '0', 0, '0', '2025-02-27 13:13:40'),
(9, 9, 0, '0', 0, '0', 0, '0', 0, '0', '2025-02-27 16:23:04'),
(10, 10, 0, '0', 0, '0', 0, '0', 0, '0', '2025-02-27 17:55:45'),
(11, 11, 0, '0', 0, '0', 0, '0', 0, '0', '2025-02-27 17:56:23'),
(12, 12, 0, '0', 0, '0', 0, '0', 0, '0', '2025-02-27 20:21:14'),
(13, 13, 0, '0', 0, '0', 0, '0', 0, '0', '2025-02-28 09:46:19'),
(14, 15, 0, '0', 0, '0', 0, '0', 0, '0', '2025-02-28 11:59:10'),
(15, 16, 0, '0', 0, '0', 0, '0', 0, '0', '2025-02-28 12:26:43');

-- --------------------------------------------------------

--
-- Table structure for table `awb_charges`
--

CREATE TABLE `awb_charges` (
  `id` int(11) NOT NULL,
  `awbID` int(11) NOT NULL,
  `chargeType1` int(11) DEFAULT NULL,
  `chargeType1Amount` decimal(10,2) DEFAULT 0.00,
  `chargeType2` int(11) DEFAULT NULL,
  `chargeType2Amount` decimal(10,2) DEFAULT 0.00,
  `chargeType3` int(11) DEFAULT NULL,
  `chargeType3Amount` decimal(10,2) DEFAULT 0.00,
  `chargeType4` int(11) DEFAULT NULL,
  `chargeType4Amount` decimal(10,2) DEFAULT 0.00,
  `storageType1` int(250) DEFAULT NULL,
  `storageType1Days` int(11) DEFAULT 0,
  `storageType1Amount` decimal(10,2) DEFAULT 0.00,
  `storageType2` int(11) DEFAULT NULL,
  `storageType2Days` int(11) DEFAULT 0,
  `storageType2Amount` decimal(10,2) DEFAULT 0.00
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `awb_charges`
--

INSERT INTO `awb_charges` (`id`, `awbID`, `chargeType1`, `chargeType1Amount`, `chargeType2`, `chargeType2Amount`, `chargeType3`, `chargeType3Amount`, `chargeType4`, `chargeType4Amount`, `storageType1`, `storageType1Days`, `storageType1Amount`, `storageType2`, `storageType2Days`, `storageType2Amount`) VALUES
(1, 1, 1, 202144.80, 0, 0.00, 0, 0.00, 0, 0.00, 0, 0, 0.00, 0, 0, 0.00),
(2, 2, NULL, 0.00, NULL, 0.00, NULL, 0.00, NULL, 0.00, NULL, 0, 0.00, NULL, 0, 0.00),
(3, 3, 1, 55130.40, 0, 0.00, 0, 0.00, 0, 0.00, 0, 0, 0.00, 0, 0, 0.00),
(4, 4, NULL, 0.00, NULL, 0.00, NULL, 0.00, NULL, 0.00, NULL, 0, 0.00, NULL, 0, 0.00),
(5, 5, NULL, 0.00, NULL, 0.00, NULL, 0.00, NULL, 0.00, NULL, 0, 0.00, NULL, 0, 0.00),
(6, 6, NULL, 0.00, NULL, 0.00, NULL, 0.00, NULL, 0.00, NULL, 0, 0.00, NULL, 0, 0.00),
(7, 7, NULL, 0.00, NULL, 0.00, NULL, 0.00, NULL, 0.00, NULL, 0, 0.00, NULL, 0, 0.00),
(8, 8, NULL, 0.00, NULL, 0.00, NULL, 0.00, NULL, 0.00, NULL, 0, 0.00, NULL, 0, 0.00),
(9, 9, NULL, 0.00, NULL, 0.00, NULL, 0.00, NULL, 0.00, NULL, 0, 0.00, NULL, 0, 0.00),
(10, 10, NULL, 0.00, NULL, 0.00, NULL, 0.00, NULL, 0.00, NULL, 0, 0.00, NULL, 0, 0.00),
(11, 11, NULL, 0.00, NULL, 0.00, NULL, 0.00, NULL, 0.00, NULL, 0, 0.00, NULL, 0, 0.00),
(12, 12, NULL, 0.00, NULL, 0.00, NULL, 0.00, NULL, 0.00, NULL, 0, 0.00, NULL, 0, 0.00),
(13, 13, NULL, 0.00, NULL, 0.00, NULL, 0.00, NULL, 0.00, NULL, 0, 0.00, NULL, 0, 0.00),
(14, 15, NULL, 0.00, NULL, 0.00, NULL, 0.00, NULL, 0.00, NULL, 0, 0.00, NULL, 0, 0.00),
(15, 16, NULL, 0.00, NULL, 0.00, NULL, 0.00, NULL, 0.00, NULL, 0, 0.00, NULL, 0, 0.00);

-- --------------------------------------------------------

--
-- Table structure for table `awb_local`
--

CREATE TABLE `awb_local` (
  `id` int(11) NOT NULL,
  `AwbNo` varchar(50) NOT NULL,
  `SerialNo` varchar(100) DEFAULT NULL,
  `HAWB` varchar(50) DEFAULT NULL,
  `ManifestID` int(11) NOT NULL,
  `TotalPieces` varchar(50) DEFAULT NULL,
  `TotalGrossWgtVol` varchar(50) DEFAULT NULL,
  `CargoDescription` varchar(50) DEFAULT NULL,
  `PointOfOrg` varchar(50) DEFAULT NULL,
  `PointOfDest` varchar(50) DEFAULT NULL,
  `CargoTypeID` int(11) DEFAULT NULL,
  `StorageTypeID` int(11) DEFAULT NULL,
  `NatureOfGoods` varchar(50) DEFAULT NULL,
  `Comment` varchar(500) DEFAULT '-',
  `OnHandWgtVol` int(11) DEFAULT NULL,
  `OnHandPieces` int(11) DEFAULT NULL,
  `Charge` varchar(100) DEFAULT NULL,
  `PaymentMethod` int(11) DEFAULT NULL,
  `InvoiceNo` varchar(100) DEFAULT NULL,
  `ChequeNo` varchar(100) DEFAULT NULL,
  `PaymentStatus` int(11) DEFAULT NULL,
  `CustomsNo` varchar(50) DEFAULT NULL,
  `CheckedInStatus` int(11) DEFAULT NULL,
  `CheckedInDate` varchar(50) DEFAULT NULL,
  `ValidationCode` varchar(50) DEFAULT NULL,
  `State` int(11) DEFAULT NULL,
  `TimeStamp` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `awb_storage_types`
--

CREATE TABLE `awb_storage_types` (
  `id` int(11) NOT NULL,
  `AwbID` int(11) NOT NULL,
  `StorageType1` int(11) DEFAULT 0,
  `StorageType1Weight` varchar(50) DEFAULT '0',
  `StorageType2` int(11) DEFAULT 0,
  `StorageType2Weight` varchar(50) DEFAULT '0',
  `TimeStamp` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `awb_storage_types`
--

INSERT INTO `awb_storage_types` (`id`, `AwbID`, `StorageType1`, `StorageType1Weight`, `StorageType2`, `StorageType2Weight`, `TimeStamp`) VALUES
(1, 1, 4, '200.0', 0, '0', '2025-02-18 11:45:00'),
(2, 2, 4, '200.0', 0, '0', '2025-02-18 11:45:23'),
(3, 3, 4, '100.0', 0, '0', '2025-02-18 11:49:58'),
(4, 4, 3, '200', 0, '0', '2025-02-18 12:26:21'),
(5, 5, 0, '0', 0, '0', '2025-02-27 02:14:21'),
(6, 6, 0, '0', 0, '0', '2025-02-27 07:41:15'),
(7, 7, 0, '0', 0, '0', '2025-02-27 09:16:00'),
(8, 8, 0, '0', 0, '0', '2025-02-27 13:13:40'),
(9, 9, 0, '0', 0, '0', '2025-02-27 16:23:04'),
(10, 10, 0, '0', 0, '0', '2025-02-27 17:55:45'),
(11, 11, 0, '0', 0, '0', '2025-02-27 17:56:23'),
(12, 12, 0, '0', 0, '0', '2025-02-27 20:21:14'),
(13, 13, 0, '0', 0, '0', '2025-02-28 09:46:19'),
(14, 15, 0, '0', 0, '0', '2025-02-28 11:59:10'),
(15, 16, 0, '0', 0, '0', '2025-02-28 12:26:43');

-- --------------------------------------------------------

--
-- Table structure for table `banks`
--

CREATE TABLE `banks` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `AccountNo` varchar(255) NOT NULL,
  `BankSwiftBIC` varchar(255) NOT NULL,
  `BankName` varchar(255) NOT NULL,
  `BankCountryCode` varchar(255) NOT NULL,
  `CreatedBy` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `banks`
--

INSERT INTO `banks` (`id`, `AccountNo`, `BankSwiftBIC`, `BankName`, `BankCountryCode`, `CreatedBy`, `created_at`, `updated_at`) VALUES
(1, '**********', '8991', 'National', 'MW', NULL, '2025-02-24 00:48:59', '2025-02-24 00:48:59'),
(2, '0023120', 'XXXXX-111', 'Standard Bank', 'MW', NULL, '2025-02-24 11:15:48', '2025-02-28 06:40:02'),
(3, '*********', 'AO00', 'ZimBank', 'AO', NULL, '2025-02-28 06:47:39', '2025-02-28 06:49:17');

-- --------------------------------------------------------

--
-- Table structure for table `cache`
--

CREATE TABLE `cache` (
  `key` varchar(255) NOT NULL,
  `value` mediumtext NOT NULL,
  `expiration` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `cache_locks`
--

CREATE TABLE `cache_locks` (
  `key` varchar(255) NOT NULL,
  `owner` varchar(255) NOT NULL,
  `expiration` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `cargo_states`
--

CREATE TABLE `cargo_states` (
  `StateID` int(11) NOT NULL,
  `StateName` varchar(50) NOT NULL,
  `StateCode` varchar(50) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `cargo_states`
--

INSERT INTO `cargo_states` (`StateID`, `StateName`, `StateCode`, `created_at`, `updated_at`) VALUES
(1, 'Complete', '', '2025-02-28 11:26:20', '2025-02-28 11:26:20'),
(2, 'Missing Cargo', 'MSCA', '2025-02-28 11:26:20', '2025-02-28 11:26:20'),
(3, 'Missing AWB', 'MSAW', '2025-02-28 11:26:20', '2025-02-28 11:26:20'),
(4, 'Damaged Cargo', 'DISC', '2025-02-28 11:26:20', '2025-02-28 11:26:20'),
(5, 'Excess Cargo', 'FDCA', '2025-02-28 11:26:20', '2025-02-28 11:26:20'),
(6, 'Excess Documents', 'FDAW', '2025-02-28 11:26:20', '2025-02-28 11:26:20');

-- --------------------------------------------------------

--
-- Table structure for table `cargo_types`
--

CREATE TABLE `cargo_types` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `CargoCode` varchar(255) NOT NULL,
  `CargoName` varchar(255) NOT NULL,
  `CargoCategory` varchar(255) DEFAULT NULL,
  `RatePerKgUsd` varchar(255) NOT NULL,
  `ChargeUsd` varchar(255) NOT NULL,
  `ChargeMKW` varchar(255) DEFAULT NULL,
  `TaxType` varchar(255) DEFAULT NULL,
  `Taxable` varchar(255) NOT NULL,
  `CreatedBy` varchar(255) DEFAULT NULL,
  `specialCargo` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `cargo_types`
--

INSERT INTO `cargo_types` (`id`, `CargoCode`, `CargoName`, `CargoCategory`, `RatePerKgUsd`, `ChargeUsd`, `ChargeMKW`, `TaxType`, `Taxable`, `CreatedBy`, `specialCargo`, `created_at`, `updated_at`) VALUES
(1, 'CHA', 'Medicinal Marijuana', 'Special', '0.12', '12', NULL, NULL, '1', NULL, '1', '2025-02-23 23:44:19', '2025-02-28 05:37:56'),
(2, 'RYF', 'Royal Flight Cargo', 'Special', '123', '34', NULL, NULL, '1', NULL, '1', '2025-02-26 23:22:51', '2025-02-26 23:22:51'),
(3, 'TX', 'Trade Tires', 'Special', '12', '12', NULL, NULL, '1', NULL, '1', '2025-02-28 05:41:23', '2025-02-28 05:41:23'),
(4, 'TST1', 'Tesst', 'No', '20', '12', NULL, NULL, '1', NULL, '0', '2025-02-28 05:43:16', '2025-02-28 05:43:16');

-- --------------------------------------------------------

--
-- Table structure for table `consignees`
--

CREATE TABLE `consignees` (
  `id` int(11) NOT NULL,
  `ConsigneeName` varchar(100) NOT NULL,
  `ConsigneeAccountNo` varchar(50) NOT NULL,
  `ConsigneeAddress1` varchar(100) NOT NULL,
  `ConsigneeAddress2` varchar(100) NOT NULL,
  `ConsigneeAddress3` varchar(100) NOT NULL,
  `ConsigneeEmail` varchar(100) NOT NULL,
  `ConsigneePhonenumber` varchar(100) DEFAULT NULL,
  `AccountType` int(11) NOT NULL,
  `AccountState` bit(1) DEFAULT NULL,
  `TaxType` int(11) NOT NULL,
  `ContactName` varchar(100) NOT NULL,
  `ContactPhone` varchar(100) NOT NULL,
  `ContactFax` varchar(100) NOT NULL,
  `ContactEmail` varchar(100) NOT NULL,
  `PaymentType` int(11) NOT NULL,
  `CreatedBy` int(11) NOT NULL,
  `TimeStamp` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `consignee_owner` varchar(250) DEFAULT NULL,
  `defaultRow` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `consignees`
--

INSERT INTO `consignees` (`id`, `ConsigneeName`, `ConsigneeAccountNo`, `ConsigneeAddress1`, `ConsigneeAddress2`, `ConsigneeAddress3`, `ConsigneeEmail`, `ConsigneePhonenumber`, `AccountType`, `AccountState`, `TaxType`, `ContactName`, `ContactPhone`, `ContactFax`, `ContactEmail`, `PaymentType`, `CreatedBy`, `TimeStamp`, `consignee_owner`, `defaultRow`, `created_at`, `updated_at`) VALUES
(0, 'N/A', '', '', 'NA', '', '', '', 2, NULL, 2, 'NA', '', '', '', 0, 0, '2024-05-09 11:13:59', '125', 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(1, 'AIRPORTS DEVELOPMENT LIMITED', 'ADL100', 'Line 1 ', 'Line 2', 'Line 3', '<EMAIL>', '', 1, NULL, 2, 'Mwemwe', '555', '555', 'mwemwe@mwemwe', 1, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(2, 'AMERICAN EMBASSY', 'AEM100', 'P.O. BOX 30016', 'CAPITAL CITY', 'LILONGWE 3', '', '**********', 1, NULL, 2, 'EMILY NAMAKHWA', '01 773 166', '', '<EMAIL>', 1, 1, '2024-04-01 21:37:17', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(3, 'AGRIQUIP', 'AGR100', 'P.O. BOX 78', 'BLANTYRE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(4, 'AGS LILONGWE LTD', 'AGS100', 'P/BAG 283', 'P/BAG 28', 'LILONGWE', '', '', 1, NULL, 2, 'Louis Fernandez', '01759374', '', '<EMAIL>', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(5, 'AIR ZIMBABWE', 'AIR100', 'P.O. BOX 30804', 'CAPITAL CITY', 'LILONGWE', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(6, 'ALLIED FREIGHT', 'ALL100', 'P.O. BOX 134', 'LUMBADZI', 'LILONGWE', '<EMAIL>', '', 2, NULL, 2, 'Maureen Kachombo', '01711198 / 0111206926', '01700343', '<EMAIL>', 2, 1, '2024-05-09 11:14:02', '3', 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(7, 'ALLIANCE ONE INTERNATIONAL', 'ALO100', 'P.O. BOX 30522', 'LILONGWE', 'MALAWI', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(8, 'BOLLORE AFRICA LOGISTICS', 'AMI100', 'P.O. BOX 838', 'BLANTYRE', 'MALAWI', '', '', 2, NULL, 2, 'BRIAN', '********', '', '<EMAIL>', 0, 1, '2025-02-18 13:26:30', '147', 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(9, 'SDV (AMI), FREIGHT SALES', 'AMI200', 'P.O. BOX 838', 'BLANTYRE', '', '', '', 2, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(10, 'ST ANDREWS INTL PRIMARY SCHOOL', 'AND200', 'P.O. BOX 593', 'BLANTYRE', '', '', '', 1, NULL, 2, 'Senior Accountant', '********', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(11, 'ANDERSON ENGINEERING', 'ANDE100', '', '', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(12, 'AVIATION PENSION FUND', 'APF100', 'P.O. BOX 84', 'BLANYTRE', '', '', '', 1, NULL, 2, 'Mr A. Jana', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(13, 'AUTOMOTIVE PRODUCTS', 'AUT100', 'P.O. BOX 30068', 'LILONGWE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(14, 'DEPARTMENT OF CIVIL AVIATION', 'AVI100', 'P.O. BOX 1', 'CHILEKA ', 'BLANTYRE', '', '', 1, NULL, 2, 'S MAULUKA', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(15, 'BURCO ELECTRONICS', 'BCO100', 'P.O. BOX 934', 'BLANTYRE', '', '', '', 1, NULL, 2, '', '01840772/777', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(16, 'BRITISH HIGH COMMISSION', 'BHC200', 'P.O. BOX 30042', 'CAPITAL CITY', 'LILONGWE 3', '', '', 1, NULL, 2, '', '01772400', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(17, 'BISHOP MACKENZIE SCHOOL', 'BIS100', 'P.O. BOX 102', 'LILONGWE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(18, 'BNC PACKAGING', 'BNC100', 'P.O. BOX 30575', 'CHICHIRI', 'BLANTYRE 3', '', '', 1, NULL, 2, 'Mrs Gondwe', '01877068', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(19, 'BLANTYRE PRINT AND PACKAGING', 'BPP100', '', '', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(20, 'BR COMPUTERS', 'BRC100', 'P.O. BOX 109', 'BLANTYRE', '', '', '', 1, NULL, 2, 'BLESSINGS TENGANI', '0888 737 654', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(21, 'BUSINESS COMPUTER SERVICES', 'BUS100', 'P.O. BOX 934', 'BLANTYRE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(22, 'BUSINESS MACHINES', 'BUS200', 'CHURCHILL ROAD', 'P.O. BOX 5095', 'LIMBE', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(23, 'CASH INVOICES', 'CAS100', '', '', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(24, 'CATERING SOLUTIONS', 'CAT100', 'P.O. BOX 941', 'BLANTYRE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(25, 'COMBINE CARGO', 'CCG100', 'P/BAG B465', 'LILONGWE', 'MALAWI', '', '', 2, NULL, 2, 'KALUWA', '0888 914 745', '', '<EMAIL>', 0, 1, '2024-04-23 06:37:56', '0', 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(26, 'COMBINE CARGO FREIGHT SALES', 'CCG200', 'P.O. BOX 31588', 'CHICHIRI', 'BLANTYRE 3', '', '', 2, NULL, 2, '', '', '', '', 0, 1, '2024-05-09 11:12:10', '0', 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(27, 'CONTINENTAL CARGO', 'CCO100', 'P.O. BOX 2193', 'LILONGWE', 'MALAWI', '', '', 2, NULL, 2, '', '01700059', '', '', 0, 1, '2024-05-08 10:25:16', '0', 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(28, 'CENTRAL MEDICAL TRUST', 'CEN100', '', '', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(29, 'C.G. COMMUNICATIONS', 'CGC100', 'P.O. BOX 2322', 'BLANTYRE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(30, 'CARGO MATE', 'CGT100', 'P.O. BOX 30094', 'CAPITAL CITY', 'LILONGWE 3', '', '', 2, NULL, 2, '', '01710102/635', '', '', 0, 1, '2024-05-09 11:14:00', '15', 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(31, 'CHARLES STEWART', 'CHARL100', '', '', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(32, 'CHANACHE FUNERALS', 'CHF100', 'P.O. BOX 1672', 'BLANTYRE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(33, 'CHIKOWA PRODUCE', 'CHI660', 'P.O. BOX 2814', 'BLANTYRE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(34, 'CHIKO GENERAL DEALERS', 'CHI700', 'C/O MR T CHIMPUKUSO', 'BLANTYRE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(35, 'JAMES CHIMWAZA', 'CHI750', 'P.O. BOX 2630', 'BLANTYRE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(36, 'CINE CITY', 'CIN100', 'P.O. BOX E176', 'POST DOT', '', '', '', 1, NULL, 2, '', '01912873', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(37, 'CLICK AFRICA LIMITED', 'CLI100', 'P.O. BOX 1327', 'LILONGWE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(38, 'CARGO MANAGEMENT LOGISTICS', 'CML100', 'CONSTRUCTION HOUSE', 'LILONGWE 4', '', '', '', 2, NULL, 2, 'F.KAPUNDA / MAUREEN', '01712 335/ 09 838985 /01700530', '', '<EMAIL>', 0, 1, '2025-02-18 12:50:03', '17', 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(39, 'COMPUBYTE', 'COM150', 'P.O. BOX 30402', 'LILONGWE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(40, 'COMMERCIAL WORLD LTD', 'COM250', 'P.O. BOX 40412', 'LILONGWE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(41, 'COMPUTER SYSTEMS', 'COM300', 'P.O. BOX 2009', 'BLANTYRE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(42, 'COMPUFIX SERVICES', 'COM350', '', '', '', '', '', 1, NULL, 2, '', '0999302280', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(43, 'COMBINE CARGO - AGENT SALES', 'COM400', 'P.O. BOX 31588', 'CHICHIRI', 'BLANTYRE 3', '', '', 2, NULL, 2, '', '', '', '', 0, 1, '2024-05-09 11:13:58', '11', 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(44, 'COMPUTER PLUS PERIPHERALS', 'COM600', 'BLANTYRE', '', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(45, 'COTTONMAN MC CANN', 'COT200', 'P.O. BOX 2473', 'BLANTYRE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(46, 'CRYSTAL WATERS RESORT', 'CRY100', 'P.O. BOX 31966', 'BLANTYRE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(47, 'CHARLES STEWART - BT', 'CSD100', 'P.O. BOX 5914', 'LIMBE', '', '', '', 1, NULL, 2, 'MR', '01471538', '', '<EMAIL>', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(48, 'CHARLES STEWART - LL', 'CSD200', 'P.O. BOX 30040', 'LILONGWE', '', '', '', 1, NULL, 2, 'MR B. MARA', '01700950', '', '<EMAIL>', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(49, 'C & S ELECTRICAL LTD', 'CSE100', '', '', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(50, 'DEPARTMENT OF CLIMATE CHANGE', 'DCC100', 'P.O. BOX 1808', 'BLANTYRE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(51, 'DEBBIE TRADING', 'DEB100', 'P.O. BOX 1498', 'LILONGWE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(52, 'DESIGNATED FREIGHT SERVICES', 'DES100', 'P.O. BOX 168', 'LUMBADZI', '', '', '', 2, NULL, 2, '', '', '', '', 0, 1, '2024-05-08 19:34:08', '12', 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(53, 'DEUS FREIGHT SERVICES', 'DEU100', 'P.O. BOX 31997', 'BLANTYRE', 'MALAWI', '', '', 2, NULL, 2, 'MRS R MTONGA', '01 8 857 414', '', 'deusfrt@yahoo. Com', 0, 1, '2024-05-03 07:38:58', '0', 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(54, 'DHL INTL MW. LTD', 'DHL100', 'P.O. BOX 1762', 'BLANTYRE', 'MALAWI', '', '', 1, NULL, 2, 'VICTOR', '01871802', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(55, 'EDULIS FOOD PROCESSORS LTD', 'EDU100', 'P.O. BOX 9', 'CHIKANGAWA', 'MZIMBA', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(56, 'ELECTRICITY GENERATION COMPANY', 'EGE100', '', '', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(57, 'EMIRATES AIRLINES', 'EMI100', 'DUBAI BEHIND AL RAMOUL POST OFFICE', 'UAE', '', '', '', 1, NULL, 2, 'ACCOUNTANT', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(58, 'TUMA ENTERPRISE JMK (ACCOUNT CLOSED),', 'ENT100', 'P.O. BOX 51060', 'BLANTYRE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(59, 'ESCOM', 'ESC100', 'P.O. BOX 2047', 'P.O. BOX', 'BLANTYRE', '', '', 1, NULL, 2, '', '********', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(60, 'ETHIOPIAN AIRLINES - LL', 'ETH100', 'P.O. BOX 30427', 'LILONGWE', 'MALAWI', '', '', 1, NULL, 2, '', '********', '', '<EMAIL>', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(61, 'ETHIOPIAN AIRLINES - BT', 'ETH200', 'P.O. BOX 560', 'BLANTYRE', 'MALAWI', '', '', 1, NULL, 2, 'Rodas Teferi', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(62, 'EYE OF THE CHILD', 'EYE100', 'P.O. BOX 32571', 'BLANYRE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(63, 'FARGO LIMITED', 'FAG100', 'P.O. BOX 5122', 'LIMBE', '', '', '', 1, NULL, 2, 'ACCOUNTANT', '********/********', '', '<EMAIL>', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(64, 'FAST CARGO', 'FAS200', 'P.O. BOX', 'LUMBADZI', '', '', '', 2, NULL, 2, '', '', '', '', 0, 1, '2025-02-18 07:53:11', '2', 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(65, 'FATTANI OFFSET PRINTERS', 'FAT100', 'P.O. BOX 1356', 'BLANTYRE', '', '', '', 1, NULL, 2, 'MR KANJO', '********', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(66, 'FDH MONEY BUREAU', 'FDH100', '', '', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(67, 'FEDEX', 'FDX100', 'P.O. BOX 30189', 'BLANTYRE', '', '', '', 1, NULL, 2, 'Harold Phiri', '01 877 501/503', '', '<EMAIL>', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(68, 'FIRST MERCHANT BANK', 'FMT100', 'PRIVATE BAG 22', 'BLANTYRE', 'MALAWI', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(69, 'FOREIGN AFFAIRS', 'FOR100', 'P.O. BOX 30315', 'LILONGWE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(70, 'WORLD WIDE MOVERS LTD', 'FRA300', 'P.O. BOX 30580', 'CAPITAL CITY', 'LILONGWE 3', '', '', 1, NULL, 2, 'MR BONGOLOLO', '******** / **********', '', '<EMAIL> / <EMAIL>', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(71, 'FREIGHT SOLUTIONS', 'FRE100', 'P.O. BOX 51296', 'LIMBE', '', '', '', 2, NULL, 2, '', '', '', '', 0, 1, '2024-05-09 11:12:10', '0', 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(72, 'GENERAL FREIGHT SERVICES', 'GEN100', 'P.O. BOX 1598', 'LILONGWE', 'MALAWI', '', '', 2, NULL, 2, 'A C KAUNDA', '********', '', '<EMAIL>', 0, 1, '2024-04-29 07:53:26', '0', 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(73, 'GESTETNER', 'GES100', 'P.O. BOX 343', 'BLANTYRE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(74, 'GLENS MALAWI LTD (AGENT SALES),', 'GLE200', 'P.O. BOX 629', 'BLANTYRE', 'MALAWI', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(75, 'GLENS MALAWI LTD', 'GLE300', 'P.O. BOX 629', 'BLANTYRE', '', '', '', 1, NULL, 2, '', '01871888', '', '<EMAIL>', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(76, 'GLOBE COMPUTERS LTD', 'GLO100', 'P.O. BOX 5491', 'LIMBE', '', '', '', 1, NULL, 2, 'Taurai', '01841044', '', '<EMAIL>', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(77, 'GLOBE ELECTRONICS LTD', 'GLO300', 'P.O. BOX 5866', 'LIMBE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(78, 'GLOBEFLIGHT  WORLDWIDE EXPRESS LIMITED', 'GLO400', 'P.O. BOX 31128', 'KAMUZU INTL AIRPORT', 'LUMBADZI', '', '', 1, NULL, 2, '', '01 700 155', '', '<EMAIL>', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(79, 'GLOBE INTERNET', 'GLO500', 'P.O. BOX 5095', 'LILONGWE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(80, 'GOLDEN ERA INVESTMENTS', 'GOL100', 'P.O. BOX 681', 'LILONGWE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(81, 'HOCKEY ASSOCIATION OF MALAWI', 'HAM100', 'P.O. BOX 5266', 'LIMBE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(82, 'HEKO TECHNOLOGIES', 'HEK100', '', '', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(83, 'HOLY TRINITY', 'HOL100', 'P.O. BOX 112', 'LUMBADZI', 'LILONGWE', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(84, 'IBRAHIM CONDE', 'IBR100', 'NAMPULACP 409', 'NAMPULAC', 'MOZAMBIQUE', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(85, 'ICL (MALAWI), LTD', 'ICL100', 'P.O. BOX 1904', 'BLANTYRE', 'MALAWI', '', '', 1, NULL, 2, 'G.A. NNENSA', '01 822 290', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(86, 'INDIAN HIGH COMMISSION', 'IND100', 'P.O. BOX 1482', 'LILONGWE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(87, 'Infoteck Systems', 'INFOT100', '', '', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(88, 'INTERCITY SERVICES', 'INT100', '', '', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(89, 'INTERMED LIMITED', 'INT200', 'P.O. BOX 956', 'LILONGWE', '', '', '', 1, NULL, 2, '', '01754740', '', '<EMAIL>', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(90, 'JAY EM CARGO', 'JAY100', 'P.O. BOX 63', 'LUMBADZI', 'LILONGWE', '', '', 2, NULL, 2, 'G MKUPU', '01700450', '', '<EMAIL>', 0, 1, '2024-05-09 11:14:00', '19', 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(91, 'JSJ FREIGHT', 'JSJ100', 'BLANTYRE', 'BLANTYRE', '', '', '', 2, NULL, 2, '', '', '', '', 0, 1, '2024-05-09 11:14:00', '0', 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(92, 'ABDUL ISHMAEL KACHALYA', 'KAC100', 'P.O. BOX 50155', 'LIMBE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(93, 'KAS FREIGHT LIMITED', 'KAS100', 'P.O. BOX 30933', 'CHICHIRI', 'BLANTYRE 3', '', '', 2, NULL, 2, 'MR MWASE', '01873044', '', '<EMAIL>', 0, 1, '2024-05-09 11:13:59', '20', 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(94, 'KASCO100', 'KASCO100', '', '', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(95, 'KENYA AIRWAYS', 'KEN100', 'P.O. BOX 30318', 'LILONGWE', 'MALAWI', '', '', 1, NULL, 2, 'MR D.CHIRAMBO', '01774227/01775893/01700267', '', '<EMAIL>', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(96, 'KUNA FREIGHT SERVICES', 'KUN100', '', '', '', '', '', 2, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', '141', 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(97, 'LAND - AIR CARGO SERVICES', 'LAN100', 'PRIVATE BAG 354', 'LILONGWE 3', '', '', '', 2, NULL, 2, ' A. G . BASIKOLO', '01727047', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(98, 'UNILEVER S .E  AFRICA', 'LEV100', 'P.O. BOX 5151', 'LIMBE', 'MALAWI', '', '', 1, NULL, 2, 'haniff or miriam', '01841100', '', '<EMAIL>', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(99, 'LILONGWE HANDLING COMPANY', 'LIH100', 'KAMUZU INTL  AIRPORT', 'P.O. BOX 89', 'LILONGWE', '', '', 1, NULL, 2, 'A Sheikh', '01700811', '', '<EMAIL>', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(100, 'LIMBE LEAF TOBACCO', 'LIM100', 'P.O. BOX 40044', 'KANENGO', 'LILONGWE', '', '', 1, NULL, 2, 'Mr Ngayaye', '0888 308 915', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(101, 'LIMA CLEARING', 'LIM200', 'P.O. BOX 56', 'LUMBADZI', 'LILONGWE', '', '', 1, NULL, 2, 'Mr Likulungwa', '01700963', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(102, 'LION EYESIGHT HOSPITAL', 'LIO100', 'C/O V.VANZARA', 'BLANTYRE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(103, 'LUSA FREIGHT SERVICES', 'LUS100', 'KAMUZU INTL AIRPORT', 'P.O. BOX 41', 'LUMBADZI', '', '', 2, NULL, 2, 'MR SADRAM', '01 700 807 / 099 23 607', '', '', 0, 1, '2024-05-09 11:12:10', '0', 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(104, 'MALAWIAN AIRLINES', 'MAA200', 'P.O. BOX 2095', 'LILONGWE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(105, 'MALAWI BLOOD TRANSFUSION SERVICE', 'MAB100', 'P.O. BOX 2681', 'BLANTYRE', 'MALAWI', '', '', 1, NULL, 2, 'SHARIF ADAM', '01622650', '', '<EMAIL>', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(106, 'MALAWI IMMIGRATION', 'MAI200', 'P.O. BOX 331', 'BLANYREP', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(107, 'MALAWI MANGOES OPERATIONS LIMITED', 'MAL100', 'P.O. BOX 31264', 'LILONGWE', 'MALAWI', '', '', 1, NULL, 2, 'Victoria', '0999207192', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(108, 'MANICA MW. LTD', 'MAN100', 'P.O. BOX 460', 'BLANTYRE', 'MALAWI', '', '', 1, NULL, 2, 'J Butao', '01876566', '', '<EMAIL>', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(109, 'MANICA MW. LTD(AGENT SALES),', 'MAN500', 'P.O. Box 460', 'BLANTYRE', 'MALAWI', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(110, 'MANENO', 'MAN600', 'P.O. BOX 180', 'LUMBADZI', '', '', '', 1, NULL, 2, 'L.NYIRENDA', '0888322272', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(111, 'MARKET FORCE', 'MAR200', 'P.O. BOX 30462', 'BLANTYRE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(112, 'MATRAK FREIGHT SERVICES', 'MAT100', 'P.O. BOX 4', 'LUMBADZI', '', '', '', 2, NULL, 2, 'MRS A KALILOMBE', '01 700 174 / 08 395 937/09 204', '', '<EMAIL>', 0, 1, '2025-02-18 07:53:11', '0', 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(113, 'MALAWI DEFENCE FORCE (CLOSED),', 'MDF100', 'P/BAG 43', 'LILONGWE', '', '', '', 1, NULL, 2, 'John Chaika', '', '', '<EMAIL>', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(114, 'MALAWI ELECTORAL COMMISSION', 'MEC100', 'P.O. BOX 30135', 'CAPITAL CITY', 'LILONGWE', '', '', 1, NULL, 2, 'Edgington Chilapondwa', '********** / **********', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(115, 'MEDICAL WORLD', 'MED100', 'P.O. BOX 51484', 'BLANTYRE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(116, 'MEDICAL CONSULTANTS', 'MED200', 'P.O. BOX 30804', 'LILONGWE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(117, 'MIGHTY GENERAL DEALERS', 'MIG100', 'BLANTYRE', '', '', '', '', 1, NULL, 2, '', '**********', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(118, 'MALAWI INSTITUTE OF JOURNALIST', 'MIJ100', 'P.O. BOX 30165', 'CHICHIRI', 'BLANTYRE 3', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(119, 'MISCELLANEOUS DEBTORS', 'MIS100', '', '', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(120, 'MALAWI NATIONAL EXAMINATION BOARD', 'MNB100', 'P.O. BOX 191', 'ZOMBA', 'MALAWI', '', '', 1, NULL, 2, '', '01525277', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(121, 'MOONLITE VIDEO', 'MOO100', 'P.O. BOX 30820', 'LILONGWE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(122, 'MOSANTO MALAWI', 'MOS100', 'P.O. BOX 30050', 'CAPITAL CITY', 'LILONGWE', '', '', 1, NULL, 2, 'MAGGIE WIRIMA', '01710144/106/285', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(123, 'MOTA ENGIL MALAWI', 'MOT100', 'P.O. BOX 31379', 'LILONGWE', '', '', '', 1, NULL, 2, 'JOAQ SEQUEIRA', '01 773 738 739', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(124, 'MOZAMBIQUE  EMBASSY', 'MOZ100', 'P.O. BOX 30579', 'CAPITAL CITY', 'LILONGWE', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(125, 'MALAWI POSTS CORPORATION', 'MPT100', 'P.O. BOX 602', 'BLANTYRE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(126, 'MARAVI FLOWERS LIMITED', 'MRV100', 'THETE RIVER ESTATE', 'P.O. BOX 1021', 'LILONGWE', '', '', 1, NULL, 2, 'PARAS DADHNIA', '01 750 710', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(127, 'MALAWI TELECOMMUNICATIONS LTD', 'MTL100', 'P.O. BOX 537', 'BLANTYRE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(128, 'MANGOCHI YOUTH SPORTS', 'MYS100', 'P.O. BOX 91', 'MANGOCHI', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(129, 'MZUZU UNIVERSITY', 'MZU100', 'P/BAG 201', 'MZUZU', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(130, 'NATIONAL BANK OF MALAWI', 'NBM100', 'P.O. BOX 945', 'BLANTYRE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(131, 'NEW  CITY CENTRE', 'NCC100', 'P.O. BOX 714', 'BLANTYRE', '', '', '', 1, NULL, 2, '', '01 620 365', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(132, 'NICO TECHNOLOGIES', 'NIC200', 'P.O. BOX 2730', 'BLANTYRE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(133, 'CHARLES NKHOMA', 'NKH100', 'LIHACO', 'P.O. BOX 84', 'LILONGWE', '', '', 1, NULL, 2, 'S.MAULUKA', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(134, 'OCEAN AIR FREIGHT SERVICES', 'OCE100', 'P.O. BOX 1812', 'LILONGWE', '', '', '', 2, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(135, 'O&M DEV CONSULTING LTD', 'ODM100', '', '', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(136, 'OFFICE WORLD', 'OFF100', 'P.O. BOX 80082', 'MASELEMA', 'BLANTYRE 8', '', '', 1, NULL, 2, 'KRISHNAN', '01 821 049 01 822 051', '', '<EMAIL>', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(137, 'AZEEM  AMED ISSA OMAR', 'OMA100', 'C/O CARGO MOVERS', 'P.O. BOX 739', 'LILONGWE', '', '', 1, NULL, 2, 'Azeem Armed Issa Omar', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(138, 'OSK FREIGHT', 'OSK100', 'C/O GLORIA OSMAN', 'P.O. BOX 31592', 'BLANTYRE', '', '', 2, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(139, 'FARUK PATEL', 'PAT250', 'P.O. BOX 139', 'LILONGWE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(140, 'PC Computers', 'PCC100', '', '', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(141, 'NAMPAK LTD', 'PIM100', 'P.O. BOX 30533', 'CHICHIRI', 'BLANTYRE 3', '', '', 1, NULL, 2, '', '01670533', '', '<EMAIL>', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(142, 'LARFAGE CEMENT', 'POR100', 'P.O. BOX 523', 'BLANTYRE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(143, 'PREMIUM TAMA TOBACCO', 'PRE200', 'P.O. BOX 40075', 'LILONGWE 3', 'MALAWI', '', '', 1, NULL, 2, 'M.Mkhwimba', '**********', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(144, 'PUMA ENERGY', 'PUM100', 'KIA', 'LILONGWE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(145, 'QUEEN ELIZABETH CENTRAL HOSPITAL', 'QEC100', 'P.O. BOX 95', 'P.O. BOX', 'BLANTYRE', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(146, 'RESERVE BANK OF MALAWI', 'RBM100', 'P.O. BOX 30063', 'LILONGWE', 'MALAWI', '', '', 1, NULL, 2, '', '********', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(147, 'MAVUTO RICHIE', 'RIC100', '', '', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(148, 'ROYAL MOTORS', 'ROY100', 'P.O. BOX 5837', 'LILONGWE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(149, 'ROOST FREIGHT SERVICES', 'RST100', 'ROOM 917 NEW AGENTS BUILDING', 'BOX 17376', 'NORKAN PARK 1631', '', '', 2, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(150, 'S & J DISCOUNT FREIGHT', 'S&J100', 'P.O. BOX 30005', 'CAPITAL CITY', 'LILONGWE', '', '', 2, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(151, 'SOUTH AFRICAN AIRWAYS', 'SAA100', 'P.O. Box 672', 'BLANTYRE', '', '', '', 1, NULL, 2, '', '01620629', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(152, 'SADM PHARMACEUTICALS LTD', 'SAD100', 'P.O. BOX 1814', 'LILONGWE', 'MALAWI', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(153, 'SAFARI FREIGHT', 'SAF100', 'P.O. BOX 51423', 'LIMBE', '', '', '', 2, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(154, 'SOUTH AFRICAN HIGH COMMISSION', 'SAH100', 'P.O. BOX 30043', 'LILONGWE', '', '', '', 1, NULL, 2, '', '01773722', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(155, 'SCHLECKER COURIER', 'SCH100', 'LILONGWE', 'LILONGWE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(156, 'GROUP 4 SECURICOR', 'SEC300', 'P.O. BOX 720', 'BLANTYRE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(157, 'SKYBAND CORPORATION LIMITED', 'SKY100', '', '', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(158, 'STANSFIELD MOTORS LTD', 'STA200', 'P.O. BOX 45', 'BLANTYRE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(159, 'STATE HOUSE', 'STE100', 'P.O. BOX 807', 'LILONGWE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(160, 'ST ANDREWS HIGH SCHOOL', 'STN100', 'PRIVATE BAG 211', 'BLANTYRE', 'MALAWI', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(161, 'STUTTAFORDS REMOVALS', 'STU100', 'P.O. BOX 2052', 'P.O. BOX ', 'LILONGWE', '', '', 1, NULL, 2, 'Lucy', '********', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(162, 'TALK OF THE TOWN (ACCOUNT CLOSED),', 'TAL100', 'P.O. BOX 1817', 'BLANTYRE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(163, 'TECHNO AUTOMOTIVE LTD (ACCOUNT CLOSED),', 'TEC200', 'P.O. BOX 51272', 'BLANTYRE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(164, 'TECHNO BRAIN', 'TEC300', 'P.O. BOX 2625', 'LILONGWE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(165, 'Thanzi Ltd', 'THANZ100', '', '', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(166, 'TELEKOM NETWORKS MALAWI', 'TNM100', 'P.O. BOX 3039', 'BLANTYRE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(167, 'TNT (GLENS),', 'TNT100', 'P.O. BOX 762', 'LILONGWE', '', '', '', 1, NULL, 2, 'JEFF MAZIZWA / KONDWANI MAGOWE', '08 301 774', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(168, 'TOYOTA MALAWI LTD', 'TOY100', 'P.O. BOX 430', 'BLANTYRE', 'MALAWI', '', '', 1, NULL, 2, 'James Tsekwe', '********', '', '<EMAIL>', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(169, 'TRADE MAL INTERNATIONAL (ACCOUNT CLOSED),', 'TRA100', 'P.O. BOX 1245', 'BLANTYRE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(170, 'TRANSMARITIME', 'TRA200', 'P.O. BOX 434', 'BLANTYRE', '', '', '', 1, NULL, 2, 'Mphatso Phikira', '01 876100/500', '', '<EMAIL>', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(171, 'TREGIA STATIONERY', 'TRE100', 'P.O. BOX 1328', 'LILONGWE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(172, 'UNIFIED TECHNOLOGIES (ACCOUNT CLOSED),', 'UNI100', '', '', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(173, 'UNION TRANSPORT (ACCOUNT CLOSED),', 'UNT100', 'P.O. BOX 40132', 'LILONGWE', 'MALAWI', '', '', 1, NULL, 2, 'MR VICTOR', '01 710 832', '', '<EMAIL>', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(174, 'UNION TRANSPORT (AGENT SALES),', 'UNT200', 'P.O. BOX 30069', 'BLANTYRE', 'MALAWI', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(175, 'UNITED PARCEL SERVICES (UPS),', 'UPS100', 'P.O. BOX 872', 'LILONGWE', 'MALAWI', '', '', 1, NULL, 2, 'ERASTO MLAMBALA', '01 775 804', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(176, 'USAID', 'USA100', 'P.O. BOX 30455', 'LILONGWE', 'MALAWI', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(177, 'WEZI MKANDAWIRE (ACCOUNT CLOSED),', 'WEZ100', '', '', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(178, 'WIRELESS BRIDGES', 'WIR100', 'P.O. BOX 32210', 'BLANTYRE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(179, 'WORLD FREIGHT INTERNATIONAL', 'WOR300', '', '', '', '', '', 2, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(180, 'WORLDNET LOGISTICS (PTY) LTD.', 'WOR400', 'Longmeadow Business Estate WestUnit 3', '3 Drakensberg Drive, Edenvale 1609, Johannesburg', 'P.O. Box 382, isando 1600, South Arica', '', '', 2, NULL, 2, 'Riana Brits - Air Charter Spec', '+27 (11), 409-9700 Switchboard', '', '<EMAIL>', 0, 1, '2024-05-09 11:12:10', '0', 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(181, 'XEROGRAPHICS LTD', 'XER100', 'P.O. BOX 872', 'LILONGWE', 'MALAWI', '', '', 1, NULL, 2, 'MR TAYUB', '01 754 756', '', '<EMAIL>', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(182, 'YAILO ENTERPRISE', 'YAI100', 'P.O. BOX 794', 'LILONGWE', '', '', '', 1, NULL, 2, '', '', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(183, 'ZIKOMO FLOWERS', 'ZIK100', 'P.O. BOX 764', 'LILONGWE', 'MALAWI', '', '', 1, NULL, 2, 'Mr Mayu  01750363', '01750341/01750128', '', '', 0, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(184, 'BLESSINGS', '', 'P.O BOX 445', 'LILONGWE', '', '<EMAIL>', '', 1, NULL, 2, '', '099999999', '', '', 1, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(186, 'McDonald', '000', 'p.o. box 123', 'lilongwe', 'malawi', '<EMAIL>`', '', 1, NULL, 2, 'Mcdonald', '123456', '1234566', '', 1, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(187, 'rte', 'tret', 'ter', 'ter', 'tre', 'tre', NULL, 1, NULL, 2, 'tre', 'ter', 'tre', '', 1, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(188, 'daaa', 'daaa', 'daaa', 'daaa', 'daaa', 'daaa', NULL, 1, NULL, 2, 'daaa', 'daaa', 'daaa', '', 1, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(189, 'montana', '2', '3', '3', '3', '3', NULL, 2, NULL, 2, '3', '3', '3', '', 2, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(190, 'fds', 'fds', 'fsd', 'fds', 'fds', 'fds', NULL, 2, NULL, 2, 'fds', 'fds', 'fds', '', 2, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(191, 'fds', 'fsd', 'fsd', 'fsd', 'fds', 'fsd', NULL, 2, NULL, 2, 'fds', 'fsd', 'fsd', '', 2, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(192, 'fds', 'fsd', 'fsd', 'fsd', 'fds', 'fsd', NULL, 2, NULL, 2, 'fds', 'fsd', 'fsd', '', 2, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(193, 'fds', 'fsd', 'fsd', 'fsd', 'fds', 'fsd', NULL, 2, NULL, 2, 'fds', 'fsd', 'fsd', '', 2, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(194, 'fds', 'fsd', 'fsd', 'fsd', 'fds', 'fsd', NULL, 2, NULL, 2, 'fds', 'fsd', 'fsd', '<EMAIL>', 2, 1, '2024-03-11 18:05:16', '0', 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(195, 'Testosss', 'fsd', 'fsd', 'fsd', 'fds', 'fds', NULL, 1, NULL, 2, 'fds', 'fds', 'fds', 'aaaa', 2, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(196, 'Timo', 'dfs', 'fds', 'fds', 'fds', 'fds', NULL, 2, NULL, 2, 'fds', 'dfs', 'fds', '', 2, 1, '2024-04-18 12:58:49', '168', 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(197, 'TADALA NKOTA', 'TAN0001', 'PÇO BOX 23444', 'LILONGWE', 'MALAWI', 'tadala.nkota@emaıl.com', NULL, 1, NULL, 2, 'Tadala Nkota', '0*********', '01111111', '', 1, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(198, 'fsd', 'fs', 'fsd', 'fsd', 'fsd', 'fsd', NULL, 1, NULL, 2, 'fs', 'fs', 'fsd', 'fsd', 1, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(199, 'das', 'das', 'ddsa', 'dsa', 'das', 'dsa', NULL, 1, NULL, 2, 'NA', '123456', '33', '3333', 2, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(200, 'agent', 'a', 'fds', 'fds', 'fds', 'fd', NULL, 2, NULL, 2, 'fds', 'fds', 'fsd', 'fds', 2, 1, '2024-04-10 12:48:33', '94', 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(201, 'Peter Shaba', 'DES100', 'P.O Box 5948', 'Lilongwe', 'Malawi', '<EMAIL>', NULL, 1, NULL, 2, 'Peter Shaba', '0995048466', '01700343', '<EMAIL>', 1, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(202, 'Patrick', 'AMI100', 'P.O Box 40407', 'Lilongwe', '', '', NULL, 1, NULL, 2, 'Daryan Gadama', '+265993847622', '', '', 1, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(203, 'TATA INVESTMENTS', '007', 'BLANTYRE', 'BLANTYRE', 'LIMBE', '<EMAIL>', NULL, 1, NULL, 2, 'CHISOMO', '0884274323', '01692635', '<EMAIL>', 1, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(204, 'CFAO MALAWI', '2525', 'KAOSHIUNG ROAD TOP MANDALA', 'BLANTYRE', 'P.O BOX 467', '<EMAIL>', NULL, 2, NULL, 2, 'MATRACK', '0986173499', '01700299', '<EMAIL>', 2, 1, '2024-03-11 18:05:16', '0', 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(205, 'TATA ZAMBIA LIMITED MALAWI', '231', 'TATA ZAMNBIA', 'P.O.BOX 2', 'MASAUKO CHIPEMBERE HIGH WAY', 'CHRISCHANA.GMAL.COM', NULL, 2, NULL, 2, 'ADAM GONTHI', '0887189095', '01700299', 'CHRISCHANA.GMAL.COM', 2, 1, '2024-03-11 18:05:16', '205', 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(206, 'NAMPARK', '123', 'NAMPARK MALAWI LIMITED', 'chichiri heavy industrial area', 'P.O BOX 46', 'madengu @gmail.com', NULL, 1, NULL, 2, 'msothi', '0999011125', '01700299', 'madengu @gmail.com', 1, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(207, 'KALUA', '2525', 'BLANTYRE', 'KAPENI', 'P.O BOX 467', 'madengu @gmail.com', NULL, 1, NULL, 1, 'MRS KALUA', '0986173499', '01700299', 'madengu @gmail.com', 2, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(208, 'CONDRIL ENGENHARIA', '231', 'KAOSHIUNG ROAD TOP MANDALA', 'BLANTYRE', 'P.O BOX 467', 'madengu @gmail.com', NULL, 2, NULL, 2, 'msothi', '0887189095', '01700299', 'madengu @gmail.com', 2, 1, '2024-03-11 18:05:16', '0', 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(209, 'Patrick', 'AMI100', 'P.O Box 40407', 'gfgfgf', '', '', NULL, 1, NULL, 2, '', '', '', '', 2, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(210, 'hysspo', 'am1', 'P.O Box 40407', 'lumbadzi ', '', '', NULL, 1, NULL, 2, '', '', '', '', 1, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(211, 'ADAM GONTHI', '6430', 'BLANTYRE', 'CHILEKA', 'CIA', '<EMAIL>', NULL, 2, NULL, 2, 'ADAM GONTHI', '0999415568', '01700299', '<EMAIL>', 1, 1, '2024-03-26 10:38:41', '0', 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(212, 'JAMES KAMESA', '0007', 'BLANTYRE', 'BLANTYRE', 'LIMBE', '<EMAIL>', NULL, 1, NULL, 2, 'james', '0999235567', '00009', '<EMAIL>', 1, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(213, 'TIMOTHY KAMESA', '009', 'BLANTYRE', 'BLANTYRE', 'LIMBE', '<EMAIL>', NULL, 1, NULL, 2, 'TIMOTHY', '0884274323', '00009', '<EMAIL>', 1, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(214, 'John Smith', '0002', 'Kanengo', 'Lilongwer', '', '', NULL, 1, NULL, 1, '', '', '', '', 1, 1, '2024-03-11 18:05:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(215, '-- NO SELECTION --', '', '', '', '', '', NULL, 1, NULL, 0, '', '', '', '', 0, 0, '2024-03-17 12:48:56', NULL, 1, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(216, '-- NO SELECTION --', '', '', '', '', '', NULL, 2, NULL, 0, '', '', '', '', 0, 0, '2025-02-18 12:45:28', '215', 1, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(222, 'CML', 'CML', 'KAMUZU INTERNATIONAL AIRPORT', 'LILONGWE', '', '', NULL, 1, NULL, 2, '', '', '', '', 2, 1, '2024-04-09 15:34:12', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(223, 'HUAWEI TECH. LTD', 'AGL', 'CHIPEMBERE HIGHWAY', 'BLANTYRE', '', '', NULL, 1, NULL, 1, 'AFRICAN GLOBAL LOGISTICS', '', '', '', 2, 1, '2024-04-09 15:43:28', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(224, 'ASEED MAKALANI', 'DFS', 'LILONGWE', 'MALAWI', '', '', NULL, 1, NULL, 2, 'RASHID', '', '', '', 1, 1, '2024-04-09 15:47:03', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(225, 'ALINAFE CHITETE', 'ALL STOP ', 'LILONGWE', 'MALAWI', '', '', NULL, 1, NULL, 2, 'DENNIS', '', '', '', 1, 1, '2024-04-09 15:49:14', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(226, 'KRIS OFFSET & SCREEN PRINTERS LIMITED', 'ACML', 'CHICHIRI', 'BLANTYRE', '', '', NULL, 1, NULL, 2, '', '', '', '', 1, 1, '2024-04-09 15:55:11', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(227, 'SPRAILY INVESTMENTS', 'ACML', 'NICO HOUSE', 'LILONGWE', '', '', NULL, 1, NULL, 2, '', '', '', '', 1, 1, '2024-04-09 15:57:30', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(228, 'FELIX BYAMUNGU', 'ACML', 'LILONGWE', 'MALAWI', '', '', NULL, 1, NULL, 2, '', '', '', '', 1, 1, '2024-04-09 15:59:45', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(229, 'LOVEMORE', 'ACML', 'LILONGWE', 'MALAWI', '', '', NULL, 1, NULL, 2, '', '', '', '', 1, 1, '2024-04-09 16:04:29', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(230, 'AUDI ALI', 'ACML', 'LILONGWE', 'MALAWI', '', '', NULL, 1, NULL, 2, '', '', '', '', 1, 1, '2024-04-09 16:15:18', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(231, 'UNC PROJECT', 'ACML', 'KAMUZU CENTRAL HOSPITAL', 'LILONGWE', '', '', NULL, 1, NULL, 2, 'YASMEEN JAMAL', '', '', '', 1, 1, '2024-04-09 18:07:30', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(232, 'AFRIBRANDS LTD-KFC', 'JAY-EM CARGO', 'ALONG HAILE SEALLAISIE ROAD', 'BLANTYRE', '', '', NULL, 1, NULL, 2, 'SHUMBA', '', '', '', 1, 1, '2024-04-09 18:11:32', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(233, 'Timothy Tumbulu', 'ACML', 'Lumbadzi', 'LIlongwe', '', '', NULL, 1, NULL, 2, '', '', '', '', 1, 1, '2024-04-10 08:44:13', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(234, 'KENNEDY LONGWE', 'ACML', 'LUMBADZI', 'LILONGWE', '', '', NULL, 1, NULL, 1, '', '', '', '', 1, 1, '2024-04-10 12:47:47', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(235, 'PUBLIC HEALTH NUTRITION GROUP', 'MPK FREIGHT', 'COLLEGE OF MEDICINE ', 'BLANTYRE', '', '', NULL, 1, NULL, 2, 'EMMANUEL KAWINGA', '', '', '', 1, 1, '2024-04-10 13:24:24', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(236, 'PRIME STAR TRADING', '000000', 'LILONGWE', 'BOX 12', '', '', NULL, 1, NULL, 2, '', '', '', '', 1, 1, '2024-04-12 05:50:47', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(237, 'NEEMA GADI', '000000', 'BOX 1776', 'LILONGWE', '', '', NULL, 1, NULL, 2, '', '', '', '', 1, 1, '2024-04-12 05:54:00', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(238, 'SUPER LIFE LTD', '000000', 'BOX 1776', 'LILONGWE', '', '', NULL, 1, NULL, 2, '', '', '', '', 1, 1, '2024-04-12 06:00:46', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(239, 'ROSE CHITERA', '000000', 'BOX 1776', 'LILONGWE', '', '', NULL, 1, NULL, 2, '', '', '', '', 1, 1, '2024-04-12 06:02:12', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(240, 'ANNIE MANGO', 'ACML', 'LILONGWE', 'MALAWI', '', '', NULL, 1, NULL, 2, 'ANNIE MANGO', '+265999483502', '', '', 1, 1, '2024-04-18 09:20:59', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(241, 'ROSE BIZIWICK', 'ACML', 'LIMBE', 'MALAWI', '', '', NULL, 1, NULL, 2, 'ROSE BIZIWICK', '+265999844189', '', '', 1, 1, '2024-04-18 09:24:16', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(242, 'SPRATLY INVESTMENTS', 'ACML', 'LILONGWE', 'MALAWI', '', '', NULL, 1, NULL, 2, '', '', '', '', 1, 1, '2024-04-18 09:26:37', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(243, 'TEC WORLD LTD', 'ACML', 'RUA MAJOR SERPINTO BEIRA ', 'MOZAMBIQUE', '', '', NULL, 1, NULL, 2, '', '', '', '', 1, 1, '2024-04-18 09:29:10', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(244, 'MOON PUFFS', 'ACML', 'LIMBE', 'MALAWI', '', '', NULL, 1, NULL, 2, '', '', '', '', 1, 1, '2024-04-18 09:30:50', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(245, 'DIRECTOR OF HEALTH AND SOCIAL WELFARE', 'ACML', 'MZIMBA', 'MALAWI', '', '', NULL, 1, NULL, 2, '', '', '', '', 1, 1, '2024-04-18 09:32:50', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(246, 'SHAYONA CEMENT CORPORATION', 'ACML', 'LILONGWE', 'MALAWI', '', '', NULL, 1, NULL, 2, '', '', '', '', 1, 1, '2024-04-18 09:41:07', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(247, 'Vincnet', 'ACML', 'Area 1', 'Lilongwe ', '', '', NULL, 1, NULL, 2, '', '', '', '', 1, 1, '2024-04-18 09:49:36', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(248, 'ECOBANK MALAWI ', 'ACML', 'LILONGWE', 'MALAWI', '', '', NULL, 1, NULL, 2, '', '', '', '', 1, 1, '2024-04-18 12:49:21', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38'),
(249, 'TOWN LINK FREIGHT SERVICES', 'ACML', 'LIMBE', 'MALAWI', '', '', NULL, 1, NULL, 2, 'TOWNLINK FREIGHT SERVICES', '+************', '', '', 1, 1, '2024-04-18 12:58:34', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38');
INSERT INTO `consignees` (`id`, `ConsigneeName`, `ConsigneeAccountNo`, `ConsigneeAddress1`, `ConsigneeAddress2`, `ConsigneeAddress3`, `ConsigneeEmail`, `ConsigneePhonenumber`, `AccountType`, `AccountState`, `TaxType`, `ContactName`, `ContactPhone`, `ContactFax`, `ContactEmail`, `PaymentType`, `CreatedBy`, `TimeStamp`, `consignee_owner`, `defaultRow`, `created_at`, `updated_at`) VALUES
(250, 'SICPA MALAWI LIMITED', 'ACML', 'LIMBE', 'BLANTYRE', '', '', NULL, 1, NULL, 2, 'EDSON MUIAMBO', '+********** 316', '', '', 1, 1, '2024-04-19 06:14:02', NULL, 0, '2025-03-01 20:43:38', '2025-03-01 20:43:38');

-- --------------------------------------------------------

--
-- Table structure for table `countries`
--

CREATE TABLE `countries` (
  `CountryID` bigint(20) UNSIGNED NOT NULL,
  `Country` varchar(255) NOT NULL,
  `Code` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `countries`
--

INSERT INTO `countries` (`CountryID`, `Country`, `Code`, `created_at`, `updated_at`) VALUES
(1, 'ANDORRA', 'AD', NULL, NULL),
(2, 'UNITED ARAB EMIRATES', 'AE', NULL, NULL),
(3, 'AFGHANISTAN', 'AF', NULL, NULL),
(4, 'ANTIGUA AND BARBUDA', 'AG', NULL, NULL),
(5, 'ANGUILLA', 'AI', NULL, NULL),
(6, 'ALBANIA', 'AL', NULL, NULL),
(7, 'ARMENIA', 'AM', NULL, NULL),
(8, 'NETHERLANDS ANTILLES', 'AN', NULL, NULL),
(9, 'ANGOLA', 'AO', NULL, NULL),
(10, 'ANTARCTICA', 'AQ', NULL, NULL),
(11, 'ARGENTINA', 'AR', NULL, NULL),
(12, 'AMERICAN SAMOA', 'AS', NULL, NULL),
(13, 'AUSTRIA', 'AT', NULL, NULL),
(14, 'AUSTRALIA', 'AU', NULL, NULL),
(15, 'ARUBA', 'AW', NULL, NULL),
(16, 'Aland Islands', 'AX', NULL, NULL),
(17, 'AZERBAIJAN', 'AZ', NULL, NULL),
(18, 'BOSNIA AND HERZEGOVINA', 'BA', NULL, NULL),
(19, 'BARBADOS', 'BB', NULL, NULL),
(20, 'BANGLADESH', 'BD', NULL, NULL),
(21, 'BELGIUM', 'BE', NULL, NULL),
(22, 'BURKINA FASO', 'BF', NULL, NULL),
(23, 'BULGARIA', 'BG', NULL, NULL),
(24, 'BAHRAIN', 'BH', NULL, NULL),
(25, 'BURUNDI', 'BI', NULL, NULL),
(26, 'BENIN', 'BJ', NULL, NULL),
(27, 'BERMUDA', 'BM', NULL, NULL),
(28, 'BRUNEI DARUSSALAM', 'BN', NULL, NULL),
(29, 'BOLIVIA', 'BO', NULL, NULL),
(30, 'BRAZIL', 'BR', NULL, NULL),
(31, 'BAHAMAS', 'BS', NULL, NULL),
(32, 'BHUTAN', 'BT', NULL, NULL),
(33, 'BOUVET ISLAND', 'BV', NULL, NULL),
(34, 'BOTSWANA', 'BW', NULL, NULL),
(35, 'BELARUS', 'BY', NULL, NULL),
(36, 'BELIZE', 'BZ', NULL, NULL),
(37, 'CANADA', 'CA', NULL, NULL),
(38, 'COCOS (KEELING) ISLANDS', 'CC', NULL, NULL),
(39, 'Congo, Democratic Republic', 'CD', NULL, NULL),
(40, 'CENTRAL AFRICAN REPUBLIC', 'CF', NULL, NULL),
(41, 'CONGO', 'CG', NULL, NULL),
(42, 'SWITZERLAND', 'CH', NULL, NULL),
(43, 'COTE DIVOIRE', 'CI', NULL, NULL),
(44, 'COOK ISLANDS', 'CK', NULL, NULL),
(45, 'CHILE', 'CL', NULL, NULL),
(46, 'CAMEROON', 'CM', NULL, NULL),
(47, 'CHINA', 'CN', NULL, NULL),
(48, 'COLOMBIA', 'CO', NULL, NULL),
(49, 'COSTA RICA', 'CR', NULL, NULL),
(50, 'Serbia and Montenegro', 'CS', NULL, NULL),
(51, 'CUBA', 'CU', NULL, NULL),
(52, 'CAPE VERDE', 'CV', NULL, NULL),
(53, 'CHRISTMAS ISLAND', 'CX', NULL, NULL),
(54, 'CYPRUS', 'CY', NULL, NULL),
(55, 'CZECH REPUBLIC', 'CZ', NULL, NULL),
(56, 'GERMANY', 'DE', NULL, NULL),
(57, 'DJIBOUTI', 'DJ', NULL, NULL),
(58, 'DENMARK', 'DK', NULL, NULL),
(59, 'DOMINICA', 'DM', NULL, NULL),
(60, 'DOMINICAN REPUBLIC', 'DO', NULL, NULL),
(61, 'ALGERIA', 'DZ', NULL, NULL),
(62, 'ECUADOR', 'EC', NULL, NULL),
(63, 'ESTONIA', 'EE', NULL, NULL),
(64, 'EGYPT', 'EG', NULL, NULL),
(65, 'WESTERN SAHARA', 'EH', NULL, NULL),
(66, 'ERITREA', 'ER', NULL, NULL),
(67, 'SPAIN', 'ES', NULL, NULL),
(68, 'ETHIOPIA', 'ET', NULL, NULL),
(69, 'FINLAND', 'FI', NULL, NULL),
(70, 'FIJI', 'FJ', NULL, NULL),
(71, 'FALKLAND ISLANDS (MALVINAS)', 'FK', NULL, NULL),
(72, 'MICRONESIA', 'FM', NULL, NULL),
(73, 'FAROE ISLANDS', 'FO', NULL, NULL),
(74, 'FRANCE', 'FR', NULL, NULL),
(75, 'GABON', 'GA', NULL, NULL),
(76, 'UNITED KINGDOM', 'GB', NULL, NULL),
(77, 'GRENADA', 'GD', NULL, NULL),
(78, 'GEORGIA', 'GE', NULL, NULL),
(79, 'FRENCH GUIANA', 'GF', NULL, NULL),
(80, 'GHANA', 'GH', NULL, NULL),
(81, 'GIBRALTAR', 'GI', NULL, NULL),
(82, 'GREENLAND', 'GL', NULL, NULL),
(83, 'GAMBIA', 'GM', NULL, NULL),
(84, 'GUINEA', 'GN', NULL, NULL),
(85, 'GUADELOUPE', 'GP', NULL, NULL),
(86, 'EQUATORIAL GUINEA', 'GQ', NULL, NULL),
(87, 'GREECE', 'GR', NULL, NULL),
(88, 'SANDWICH ISLANDS', 'GS', NULL, NULL),
(89, 'GUATEMALA', 'GT', NULL, NULL),
(90, 'GUAM', 'GU', NULL, NULL),
(91, 'GUINEA-BISSAU', 'GW', NULL, NULL),
(92, 'GUYANA', 'GY', NULL, NULL),
(93, 'Hong Kong', 'HK', NULL, NULL),
(94, 'HEARD AND MC DONALD ISLANDS', 'HM', NULL, NULL),
(95, 'HONDURAS', 'HN', NULL, NULL),
(96, 'CROATIA', 'HR', NULL, NULL),
(97, 'HAITI', 'HT', NULL, NULL),
(98, 'HUNGARY', 'HU', NULL, NULL),
(99, 'INDONESIA', 'ID', NULL, NULL),
(100, 'IRELAND', 'IE', NULL, NULL),
(101, 'ISRAEL', 'IL', NULL, NULL),
(102, 'INDIA', 'IN', NULL, NULL),
(103, 'BRITISH INDIAN OCEAN TERRITORY', 'IO', NULL, NULL),
(104, 'IRAQ', 'IQ', NULL, NULL),
(105, 'IRAN (ISLAMIC REPUBLIC OF)', 'IR', NULL, NULL),
(106, 'ICELAND', 'IS', NULL, NULL),
(107, 'ITALY', 'IT', NULL, NULL),
(108, 'JAMAICA', 'JM', NULL, NULL),
(109, 'JORDAN', 'JO', NULL, NULL),
(110, 'JAPAN', 'JP', NULL, NULL),
(111, 'KENYA', 'KE', NULL, NULL),
(112, 'KYRGYSTAN', 'KG', NULL, NULL),
(113, 'CAMBODIA', 'KH', NULL, NULL),
(114, 'KIRIBATI', 'KI', NULL, NULL),
(115, 'COMOROS', 'KM', NULL, NULL),
(116, 'SAINT KITTS AND NEVIS', 'KN', NULL, NULL),
(117, 'KOREA, DEMOCRATIC PEOPLES REP', 'KP', NULL, NULL),
(118, 'KOREA, REPUBLIC OF', 'KR', NULL, NULL),
(119, 'KUWAIT', 'KW', NULL, NULL),
(120, 'CAYMAN ISLANDS', 'KY', NULL, NULL),
(121, 'KAZAKHSTAN', 'KZ', NULL, NULL),
(122, 'LAO PEOPLES DEMOCRATIC REPUBLIC', 'LA', NULL, NULL),
(123, 'LEBANON', 'LB', NULL, NULL),
(124, 'SAINT LUCIA', 'LC', NULL, NULL),
(125, 'LIECHTENSTEIN', 'LI', NULL, NULL),
(126, 'SRI LANKA', 'LK', NULL, NULL),
(127, 'LIBERIA', 'LR', NULL, NULL),
(128, 'LESOTHO', 'LS', NULL, NULL),
(129, 'LITHUANIA', 'LT', NULL, NULL),
(130, 'LUXEMBOURG', 'LU', NULL, NULL),
(131, 'LATVIA', 'LV', NULL, NULL),
(132, 'LIBYAN ARAB JAMAHIRIYA', 'LY', NULL, NULL),
(133, 'MOROCCO', 'MA', NULL, NULL),
(134, 'MONACO', 'MC', NULL, NULL),
(135, 'MOLDOVA', 'MD', NULL, NULL),
(136, 'MADAGASCAR', 'MG', NULL, NULL),
(137, 'MARSHALL ISLANDS', 'MH', NULL, NULL),
(138, 'MACEDONIA', 'MK', NULL, NULL),
(139, 'MALI', 'ML', NULL, NULL),
(140, 'MYANMAR', 'MM', NULL, NULL),
(141, 'MONGOLIA', 'MN', NULL, NULL),
(142, 'MACAU', 'MO', NULL, NULL),
(143, 'NORTHERN MARIANA ISLANDS', 'MP', NULL, NULL),
(144, 'MARTINIQUE', 'MQ', NULL, NULL),
(145, 'MAURITANIA', 'MR', NULL, NULL),
(146, 'MONTSERRAT', 'MS', NULL, NULL),
(147, 'MALTA', 'MT', NULL, NULL),
(148, 'MAURITIUS', 'MU', NULL, NULL),
(149, 'MALDIVES', 'MV', NULL, NULL),
(150, 'MALAWI', 'MW', NULL, NULL),
(151, 'MEXICO', 'MX', NULL, NULL),
(152, 'MALAYSIA', 'MY', NULL, NULL),
(153, 'MOZAMBIQUE', 'MZ', NULL, NULL),
(154, 'NAMIBIA', 'NA', NULL, NULL),
(155, 'NEW CALEDONIA', 'NC', NULL, NULL),
(156, 'NIGER', 'NE', NULL, NULL),
(157, 'NORFOLK ISLAND', 'NF', NULL, NULL),
(158, 'NIGERIA', 'NG', NULL, NULL),
(159, 'NICARAGUA', 'NI', NULL, NULL),
(160, 'NETHERLANDS', 'NL', NULL, NULL),
(161, 'NORWAY', 'NO', NULL, NULL),
(162, 'NEPAL', 'NP', NULL, NULL),
(163, 'NAURU', 'NR', NULL, NULL),
(164, 'NIUE', 'NU', NULL, NULL),
(165, 'NEW ZEALAND', 'NZ', NULL, NULL),
(166, 'OMAN', 'OM', NULL, NULL),
(167, 'PANAMA', 'PA', NULL, NULL),
(168, 'PERU', 'PE', NULL, NULL),
(169, 'FRENCH POLYNESIA', 'PF', NULL, NULL),
(170, 'PAPUA NEW GUINEA', 'PG', NULL, NULL),
(171, 'PHILIPPINES', 'PH', NULL, NULL),
(172, 'PAKISTAN', 'PK', NULL, NULL),
(173, 'POLAND', 'PL', NULL, NULL),
(174, 'SAINT PIERRE AND MIQUELON', 'PM', NULL, NULL),
(175, 'PITCAIRN', 'PN', NULL, NULL),
(176, 'PUERTO RICO', 'PR', NULL, NULL),
(177, 'Palau', 'PS', NULL, NULL),
(178, 'PORTUGAL', 'PT', NULL, NULL),
(179, 'PALAU', 'PW', NULL, NULL),
(180, 'PARAGUAY', 'PY', NULL, NULL),
(181, 'QATAR', 'QA', NULL, NULL),
(182, 'REUNION', 'RE', NULL, NULL),
(183, 'ROMANIA', 'RO', NULL, NULL),
(184, 'RUSSIAN FEDERATION', 'RU', NULL, NULL),
(185, 'RWANDA', 'RW', NULL, NULL),
(186, 'SAUDI ARABIA', 'SA', NULL, NULL),
(187, 'SOLOMON ISLANDS', 'SB', NULL, NULL),
(188, 'SEYCHELLES', 'SC', NULL, NULL),
(189, 'SUDAN', 'SD', NULL, NULL),
(190, 'SWEDEN', 'SE', NULL, NULL),
(191, 'SINGAPORE', 'SG', NULL, NULL),
(192, 'SAINT HELENA', 'SH', NULL, NULL),
(193, 'SLOVENIA', 'SI', NULL, NULL),
(194, 'SVALBARD AND JAN MAYEN ISLANDS', 'SJ', NULL, NULL),
(195, 'SLOVAKIA', 'SK', NULL, NULL),
(196, 'SIERRA LEONE', 'SL', NULL, NULL),
(197, 'SAN MARINO', 'SM', NULL, NULL),
(198, 'SENEGAL', 'SN', NULL, NULL),
(199, 'SOMALIA', 'SO', NULL, NULL),
(200, 'SURINAME', 'SR', NULL, NULL),
(201, 'SAO TOME AND PRINCIPE', 'ST', NULL, NULL),
(202, 'EL SALVADOR', 'SV', NULL, NULL),
(203, 'SYRIAN ARAB REPUBLIC', 'SY', NULL, NULL),
(204, 'SWAZILAND', 'SZ', NULL, NULL),
(205, 'TURKS AND CAICOS ISLANDS', 'TC', NULL, NULL),
(206, 'CHAD', 'TD', NULL, NULL),
(207, 'FRENCH SOUTHERN TERRITORIES', 'TF', NULL, NULL),
(208, 'TOGO', 'TG', NULL, NULL),
(209, 'THAILAND', 'TH', NULL, NULL),
(210, 'TAJIKISTAN', 'TJ', NULL, NULL),
(211, 'TOKELAU', 'TK', NULL, NULL),
(212, 'East Timor', 'TL', NULL, NULL),
(213, 'TURKMENISTAN', 'TM', NULL, NULL),
(214, 'TUNISIA', 'TN', NULL, NULL),
(215, 'TONGA', 'TO', NULL, NULL),
(216, 'EAST TIMOR', 'TP', NULL, NULL),
(217, 'TURKEY', 'TR', NULL, NULL),
(218, 'TRINIDAD AND TOBAGO', 'TT', NULL, NULL),
(219, 'TUVALU', 'TV', NULL, NULL),
(220, 'TAIWAN, REPUBLIC OF CHINA', 'TW', NULL, NULL),
(221, 'TANZANIA, UNITED REPUBLIC OF', 'TZ', NULL, NULL),
(222, 'UKRAINE', 'UA', NULL, NULL),
(223, 'UGANDA', 'UG', NULL, NULL),
(224, 'UNITED STATES MINOR OUTLYING ISL', 'UM', NULL, NULL),
(225, 'UNITED STATES', 'US', NULL, NULL),
(226, 'URUGUAY', 'UY', NULL, NULL),
(227, 'UZBEKISTAN', 'UZ', NULL, NULL),
(228, 'VATICAN CITY STATE (HOLY SEE)', 'VA', NULL, NULL),
(229, 'SAINT VINCENT AND THE GRENADINES', 'VC', NULL, NULL),
(230, 'VENEZUELA', 'VE', NULL, NULL),
(231, 'VIRGIN ISLANDS (BRITISH)', 'VG', NULL, NULL),
(232, 'VIRGIN ISLANDS (U.S.)', 'VI', NULL, NULL),
(233, 'VIET NAM', 'VN', NULL, NULL),
(234, 'VANUATU', 'VU', NULL, NULL),
(235, 'WALLIS AND FUTUNA ISLANDS', 'WF', NULL, NULL),
(236, 'SAMOA', 'WS', NULL, NULL),
(237, 'YEMEN', 'YE', NULL, NULL),
(238, 'MAYOTTE', 'YT', NULL, NULL),
(239, 'YUGOSLAVIA', 'YU', NULL, NULL),
(240, 'SOUTH AFRICA', 'ZA', NULL, NULL),
(241, 'ZAMBIA', 'ZM', NULL, NULL),
(242, 'ZAIRE', 'ZR', NULL, NULL),
(243, 'ZIMBABWE', 'ZW', NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `failed_jobs`
--

CREATE TABLE `failed_jobs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `connection` text NOT NULL,
  `queue` text NOT NULL,
  `payload` longtext NOT NULL,
  `exception` longtext NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `jobs`
--

CREATE TABLE `jobs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `queue` varchar(255) NOT NULL,
  `payload` longtext NOT NULL,
  `attempts` tinyint(3) UNSIGNED NOT NULL,
  `reserved_at` int(10) UNSIGNED DEFAULT NULL,
  `available_at` int(10) UNSIGNED NOT NULL,
  `created_at` int(10) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `job_batches`
--

CREATE TABLE `job_batches` (
  `id` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `total_jobs` int(11) NOT NULL,
  `pending_jobs` int(11) NOT NULL,
  `failed_jobs` int(11) NOT NULL,
  `failed_job_ids` longtext NOT NULL,
  `options` mediumtext DEFAULT NULL,
  `cancelled_at` int(11) DEFAULT NULL,
  `created_at` int(11) NOT NULL,
  `finished_at` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `manifests`
--

CREATE TABLE `manifests` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `StageStatus` varchar(255) NOT NULL DEFAULT '0',
  `Airline` varchar(255) NOT NULL,
  `FlightNo` varchar(255) NOT NULL,
  `FlightDate` varchar(255) NOT NULL,
  `PointOfLoading` varchar(255) NOT NULL,
  `PointOfUnloading` varchar(255) NOT NULL,
  `Status` varchar(255) DEFAULT '0',
  `CreatedBy` varchar(255) DEFAULT NULL,
  `IsItLocalFlight` varchar(255) NOT NULL DEFAULT '0',
  `IsRoyalFlight` varchar(255) NOT NULL,
  `RoyalFlightWeight` varchar(255) DEFAULT NULL,
  `RoyalFreightConsignee` varchar(255) DEFAULT NULL,
  `InvoiceNo` varchar(255) DEFAULT NULL,
  `Charge` varchar(255) DEFAULT '0',
  `ChargeMWK` varchar(255) NOT NULL DEFAULT '0',
  `CurrencyID` varchar(255) NOT NULL DEFAULT '2',
  `PaymentStatus` varchar(255) NOT NULL DEFAULT '0',
  `PaymentMethod` varchar(255) NOT NULL DEFAULT '0',
  `isTransit` varchar(255) DEFAULT '0',
  `finalized` varchar(255) DEFAULT '0',
  `dateOfFinalization` varchar(255) DEFAULT NULL,
  `finalizeUser` varchar(255) DEFAULT NULL,
  `transitPointOfUnloading` varchar(255) DEFAULT NULL,
  `reverseFinalizeUser` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `manifests`
--

INSERT INTO `manifests` (`id`, `StageStatus`, `Airline`, `FlightNo`, `FlightDate`, `PointOfLoading`, `PointOfUnloading`, `Status`, `CreatedBy`, `IsItLocalFlight`, `IsRoyalFlight`, `RoyalFlightWeight`, `RoyalFreightConsignee`, `InvoiceNo`, `Charge`, `ChargeMWK`, `CurrencyID`, `PaymentStatus`, `PaymentMethod`, `isTransit`, `finalized`, `dateOfFinalization`, `finalizeUser`, `transitPointOfUnloading`, `reverseFinalizeUser`, `created_at`, `updated_at`) VALUES
(2, '0', 'ETH1', '432', '2025-03-05', '2', '3', '0', '1', '0', 'true', '23', '1', NULL, '2829', '345138', '2', '0', '0', 'false', '0', NULL, NULL, '3', NULL, '2025-02-27 16:07:20', '2025-02-27 16:07:20'),
(3, '0', '13p0', '1232', '2025-03-08', '1', '4', '0', '1', '0', 'false', NULL, NULL, NULL, '', '', '2', '0', '0', 'false', NULL, NULL, NULL, '4', NULL, '2025-02-27 16:11:11', '2025-02-27 16:11:11'),
(4, '0', 'ETH1', '0000', '2025-03-01', '1', '3', '0', '1', '0', 'false', NULL, NULL, NULL, '', '', '2', '0', '0', 'false', NULL, NULL, NULL, '3', NULL, '2025-02-28 20:24:03', '2025-02-28 20:24:03'),
(5, '0', '134', 'EM312', '2025-03-01', '1', '3', '0', '1', '0', 'false', NULL, NULL, NULL, '', '', '2', '0', '0', 'false', '0', NULL, NULL, '3', NULL, '2025-03-01 06:20:55', '2025-03-01 06:20:55');

-- --------------------------------------------------------

--
-- Table structure for table `manifest_consignee_allocations`
--

CREATE TABLE `manifest_consignee_allocations` (
  `id` int(11) NOT NULL,
  `AwbID` int(11) NOT NULL,
  `ConsigneeID` int(11) NOT NULL,
  `AgentID` int(11) NOT NULL,
  `ChargeType` int(11) NOT NULL,
  `ChargeAmount` varchar(50) NOT NULL DEFAULT '0',
  `CreatedBy` int(11) NOT NULL DEFAULT 0,
  `CurrencyID` int(11) NOT NULL,
  `ReportOrder` int(11) DEFAULT NULL,
  `TimeStamp` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `manifest_consignee_allocations`
--

INSERT INTO `manifest_consignee_allocations` (`id`, `AwbID`, `ConsigneeID`, `AgentID`, `ChargeType`, `ChargeAmount`, `CreatedBy`, `CurrencyID`, `ReportOrder`, `TimeStamp`, `created_at`, `updated_at`) VALUES
(4, 2, 215, 216, 2, '', 1, 1, NULL, '2025-02-18 12:45:28', '2025-03-01 21:03:11', '2025-03-01 21:03:11'),
(5, 1, 17, 38, 2, '', 1, 1, NULL, '2025-02-18 12:45:28', '2025-03-01 21:03:11', '2025-03-01 21:03:11'),
(7, 3, 17, 38, 2, '', 1, 1, NULL, '2025-02-18 12:50:03', '2025-03-01 21:03:11', '2025-03-01 21:03:11'),
(8, 4, 147, 8, 2, '', 1, 1, NULL, '2025-02-18 13:26:30', '2025-03-01 21:03:11', '2025-03-01 21:03:11'),
(9, 13, 125, 0, 0, '0', 1, 0, NULL, '2025-02-28 11:44:17', '2025-03-01 21:03:11', '2025-03-01 21:03:11'),
(10, 16, 125, 0, 0, '0', 1, 0, NULL, '2025-02-28 14:18:19', '2025-03-01 21:03:11', '2025-03-01 21:03:11'),
(12, 21, 125, 0, 0, '0', 1, 0, NULL, '2025-03-01 21:04:43', '2025-03-01 19:04:43', '2025-03-01 19:04:43');

-- --------------------------------------------------------

--
-- Table structure for table `migrations`
--

CREATE TABLE `migrations` (
  `id` int(10) UNSIGNED NOT NULL,
  `migration` varchar(255) NOT NULL,
  `batch` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `migrations`
--

INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES
(4, '0001_01_01_000000_create_users_table', 1),
(5, '0001_01_01_000001_create_cache_table', 1),
(6, '0001_01_01_000002_create_jobs_table', 1),
(7, '2025_02_23_213508_create_airlines_table', 2),
(8, '2025_02_23_224330_create_airports_table', 3),
(9, '2025_02_23_230903_create_countries_table', 4),
(10, '2025_02_24_001943_create_cargo_types_table', 5),
(11, '2025_02_24_020516_create_banks_table', 6),
(12, '2025_02_24_145034_create_storage_types_table', 7),
(13, '2025_02_25_002048_create_consignees_table', 8),
(14, '2025_02_25_050439_create_agents_table', 9),
(16, '2025_02_26_082828_create_manifests_table', 10),
(17, '2025_02_26_192950_create_accounts_configs_table', 11),
(18, '2025_02_27_085406_create_awbs_table', 12),
(19, '2025_02_27_102114_create_user_roles_table', 12);

-- --------------------------------------------------------

--
-- Table structure for table `password_reset_tokens`
--

CREATE TABLE `password_reset_tokens` (
  `email` varchar(255) NOT NULL,
  `token` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `sessions`
--

CREATE TABLE `sessions` (
  `id` varchar(255) NOT NULL,
  `user_id` bigint(20) UNSIGNED DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `payload` longtext NOT NULL,
  `last_activity` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `sessions`
--

INSERT INTO `sessions` (`id`, `user_id`, `ip_address`, `user_agent`, `payload`, `last_activity`) VALUES
('eXjSqd0FT321WYerhlzsaxI1naRYmMFF8xmzmPO0', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoicElnTzNVRHZPTWNBb1hDOEZYT3F1bmNLUUJLb2ttN1lyeHhOQ3lpUyI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjc6Imh0dHA6Ly8xMjcuMC4wLjE6ODAwMC9sb2dpbiI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fX0=', 1740871290);

-- --------------------------------------------------------

--
-- Table structure for table `storage_types`
--

CREATE TABLE `storage_types` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `StorageCode` varchar(255) NOT NULL,
  `StorageName` varchar(255) NOT NULL,
  `RatePerKgUsdStorage` varchar(255) NOT NULL,
  `ChargeUsdStorage` varchar(255) NOT NULL,
  `useStartChargingDate` varchar(255) NOT NULL,
  `CreatedBy` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `storage_types`
--

INSERT INTO `storage_types` (`id`, `StorageCode`, `StorageName`, `RatePerKgUsdStorage`, `ChargeUsdStorage`, `useStartChargingDate`, `CreatedBy`, `created_at`, `updated_at`) VALUES
(1, 'CST', 'cold storage', '0.23', '21', '1', NULL, '2025-02-24 13:21:56', '2025-02-28 06:23:35'),
(2, 'STG', 'Storage', '0.23', '21', '1', NULL, '2025-02-24 13:26:32', '2025-02-28 06:23:53');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `first_name` varchar(255) NOT NULL,
  `last_name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `branch` varchar(255) NOT NULL,
  `role` varchar(255) NOT NULL,
  `status` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `username` varchar(100) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `first_name`, `last_name`, `email`, `branch`, `role`, `status`, `password`, `created_at`, `updated_at`, `username`) VALUES
(2, 'mark', 'siyado', '<EMAIL>', 'LLW', 'admin', 'new', '$2y$12$s5pXcagOA0dL7g/Hx4FBsuFGHhAxyjrTDxVahXcpOGlF0RuUUxc1y', '2025-02-23 15:58:13', '2025-02-23 15:58:13', ''),
(3, 'mark', 'siyado', '<EMAIL>', 'kia', 'admin', 'new', '$2y$12$POhmQ9PRCaaAYmHqG96LFuE1pumIdalIySmmVxb.OkEsAwKzTA3LG', '2025-02-23 16:12:49', '2025-02-23 16:12:49', ''),
(4, 'mark', 'siyado', '<EMAIL>', 'kia', 'admin', 'new', '$2y$12$oxhmG4Qv9OlQgEQHB7ujsuMk1n0yqFxwUbYmc5bs8oMUjcxp0JFK6', '2025-02-23 16:13:33', '2025-02-23 16:13:33', ''),
(5, 'mark', 'siyado', '<EMAIL>', 'kia', 'archive', 'new', '$2y$12$rqgO110WAkCO.K0.sqJUT.M6/kLPB4jECNC0zZueAMUpVuNh9G2Am', '2025-02-23 16:16:19', '2025-02-23 16:16:19', ''),
(6, 'mark', 'siyado', '<EMAIL>', 'LLW', '1', 'active', '$2y$12$WFtbdx7SjZhg6nMLT7MTG..iWjE52C70wfjT50V.tFEXywO/AUK4i', '2025-02-24 11:35:56', '2025-02-26 06:08:44', '<EMAIL>'),
(7, 'Try', 'Me', '<EMAIL>', 'chileka', 'admin', 'active', '$2y$12$yG/RRbFnYDdvfV3pDa5mWOJfu9KNcSoHL5FPZ8Tvz.Glw9Ln2jfRe', '2025-02-25 10:47:56', '2025-02-25 11:01:28', '<EMAIL>'),
(8, 'Try 2', 'Me', '<EMAIL>', 'LLW', 'admin', 'new', '$2y$12$ZF3slLpHMeKKCNWR6CXhA.GfIIJoGIXf3tVIONXu4tNpvdqqtmu06', '2025-02-26 13:12:04', '2025-02-26 13:12:04', '<EMAIL>'),
(9, 'Try 3', 'Me', '<EMAIL>', 'LLW', 'archive', 'new', '$2y$12$aGATju/lcu9GGIdMBSwtUOX7UAKps1H3KOq/5xgizhjbDHsqWldRG', '2025-02-27 08:37:17', '2025-02-27 08:37:17', '<EMAIL>'),
(10, 'Try 4', 'Me', '<EMAIL>', 'BLZ', '1', 'new', '$2y$12$10J54AaGpQVcbHk3/MCXVewCZ5OFR39ySIM9yzMUpzC1N18X.ekpi', '2025-02-27 08:40:54', '2025-02-27 08:40:54', '<EMAIL>');

-- --------------------------------------------------------

--
-- Table structure for table `user_roles`
--

CREATE TABLE `user_roles` (
  `id` int(11) NOT NULL,
  `UserRole` varchar(50) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `user_roles`
--

INSERT INTO `user_roles` (`id`, `UserRole`) VALUES
(1, 'Admin'),
(2, 'Accountant'),
(3, 'Clerk'),
(4, 'Supervisor'),
(5, 'Security'),
(6, 'Cashier'),
(7, 'MRA'),
(8, 'Archive'),
(9, 'DOP'),
(10, 'DOF'),
(11, 'Cargo Officer'),
(12, 'Executive'),
(13, 'IT');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `accounts_configs`
--
ALTER TABLE `accounts_configs`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `agents`
--
ALTER TABLE `agents`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `airlines`
--
ALTER TABLE `airlines`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `airlines_airlinecode_unique` (`AirlineCode`),
  ADD UNIQUE KEY `airlines_airlinename_unique` (`AirlineName`);

--
-- Indexes for table `airports`
--
ALTER TABLE `airports`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `airports_airportcode_unique` (`AirportCode`),
  ADD UNIQUE KEY `airports_airportname_unique` (`AirportName`);

--
-- Indexes for table `awbs`
--
ALTER TABLE `awbs`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `awb_action_audit`
--
ALTER TABLE `awb_action_audit`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `action` (`action`);

--
-- Indexes for table `awb_audit_table`
--
ALTER TABLE `awb_audit_table`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `awb_cargo_states`
--
ALTER TABLE `awb_cargo_states`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `awb_cargo_types`
--
ALTER TABLE `awb_cargo_types`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `awb_charges`
--
ALTER TABLE `awb_charges`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `awb_local`
--
ALTER TABLE `awb_local`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `awb_storage_types`
--
ALTER TABLE `awb_storage_types`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `banks`
--
ALTER TABLE `banks`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `banks_accountno_unique` (`AccountNo`),
  ADD UNIQUE KEY `banks_bankswiftbic_unique` (`BankSwiftBIC`);

--
-- Indexes for table `cache`
--
ALTER TABLE `cache`
  ADD PRIMARY KEY (`key`);

--
-- Indexes for table `cache_locks`
--
ALTER TABLE `cache_locks`
  ADD PRIMARY KEY (`key`);

--
-- Indexes for table `cargo_states`
--
ALTER TABLE `cargo_states`
  ADD PRIMARY KEY (`StateID`);

--
-- Indexes for table `cargo_types`
--
ALTER TABLE `cargo_types`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `cargo_types_cargocode_unique` (`CargoCode`),
  ADD UNIQUE KEY `cargo_types_cargoname_unique` (`CargoName`);

--
-- Indexes for table `consignees`
--
ALTER TABLE `consignees`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `countries`
--
ALTER TABLE `countries`
  ADD PRIMARY KEY (`CountryID`);

--
-- Indexes for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`);

--
-- Indexes for table `jobs`
--
ALTER TABLE `jobs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `jobs_queue_index` (`queue`);

--
-- Indexes for table `job_batches`
--
ALTER TABLE `job_batches`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `manifests`
--
ALTER TABLE `manifests`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `manifest_consignee_allocations`
--
ALTER TABLE `manifest_consignee_allocations`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `migrations`
--
ALTER TABLE `migrations`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `password_reset_tokens`
--
ALTER TABLE `password_reset_tokens`
  ADD PRIMARY KEY (`email`);

--
-- Indexes for table `sessions`
--
ALTER TABLE `sessions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `sessions_user_id_index` (`user_id`),
  ADD KEY `sessions_last_activity_index` (`last_activity`);

--
-- Indexes for table `storage_types`
--
ALTER TABLE `storage_types`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `storage_types_storagecode_unique` (`StorageCode`),
  ADD UNIQUE KEY `storage_types_storagename_unique` (`StorageName`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `users_email_unique` (`email`);

--
-- Indexes for table `user_roles`
--
ALTER TABLE `user_roles`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `accounts_configs`
--
ALTER TABLE `accounts_configs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `agents`
--
ALTER TABLE `agents`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `airlines`
--
ALTER TABLE `airlines`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=23;

--
-- AUTO_INCREMENT for table `airports`
--
ALTER TABLE `airports`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `awbs`
--
ALTER TABLE `awbs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=23;

--
-- AUTO_INCREMENT for table `awb_action_audit`
--
ALTER TABLE `awb_action_audit`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `awb_audit_table`
--
ALTER TABLE `awb_audit_table`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `awb_cargo_states`
--
ALTER TABLE `awb_cargo_states`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=17;

--
-- AUTO_INCREMENT for table `awb_cargo_types`
--
ALTER TABLE `awb_cargo_types`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- AUTO_INCREMENT for table `awb_charges`
--
ALTER TABLE `awb_charges`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- AUTO_INCREMENT for table `awb_local`
--
ALTER TABLE `awb_local`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `awb_storage_types`
--
ALTER TABLE `awb_storage_types`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- AUTO_INCREMENT for table `banks`
--
ALTER TABLE `banks`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `cargo_types`
--
ALTER TABLE `cargo_types`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `consignees`
--
ALTER TABLE `consignees`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=251;

--
-- AUTO_INCREMENT for table `countries`
--
ALTER TABLE `countries`
  MODIFY `CountryID` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=244;

--
-- AUTO_INCREMENT for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `jobs`
--
ALTER TABLE `jobs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `manifests`
--
ALTER TABLE `manifests`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `manifest_consignee_allocations`
--
ALTER TABLE `manifest_consignee_allocations`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `migrations`
--
ALTER TABLE `migrations`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=20;

--
-- AUTO_INCREMENT for table `storage_types`
--
ALTER TABLE `storage_types`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `user_roles`
--
ALTER TABLE `user_roles`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
