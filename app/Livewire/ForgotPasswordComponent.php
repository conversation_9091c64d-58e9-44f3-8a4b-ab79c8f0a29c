<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\users;

class ForgotPasswordComponent extends Component
{
    public $username;
    public $password="123456";

    public function forgetPass(){
        $verify=users::where('username',$this->username);
        if($verify->count() > 0){
            $account=users::where('username',$this->username)->first();
            $account->password=bcrypt($this->password);
            $account->status='new';
            if($account->save()){
                return redirect()->route('login')->with('success','password reset was successfull');
            }

        }else if($verify->count() ==0){

            return redirect()->route('account-forgot_password')->with('message','provided username/email not available');

        }
    }

    public function render()
    {
        return view('livewire.forgot-password-component')->layout('layouts.app2');
    }
}
