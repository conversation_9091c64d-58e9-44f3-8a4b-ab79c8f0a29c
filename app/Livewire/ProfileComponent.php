<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\users;
use App\Models\user_roles;

class ProfileComponent extends Component
{
    public $password;
    public $userid;
    public $roleID;
    public function changePassword(){
        $this->userid=auth()->user()->id;
        $this->roleID=auth()->user()->role;
        $change=users::where('id',$this->userid)->first();
        $change->password=bcrypt($this->password);
        if($change->save()){
            return redirect()->route('logout')->with('success','password changed, login again with new password');
        }

    }
    public function render()
    {
        $profile=user_roles::where('id',$this->roleID)->first();
        return view('livewire.profile-component',compact('profile'));
    }
}
