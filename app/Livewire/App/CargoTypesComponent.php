<?php

namespace App\Livewire\App;

use Livewire\Component;
use App\Models\cargo_types;

class CargoTypesComponent extends Component
{
    public $specialCargo;
    public $code;
    public $name;
    public $taxable;
    public $charge_usd;
    public $rate_per_kg;
    public $category="";
    public $cargoid;

    public function addCargo(){

        if($this->specialCargo==1){
            $this->category="Special";

        }else{
            $this->category="No";
        }

        $check=cargo_types::where('CargoCode',$this->code);
        if($check->count() > 0){
            return redirect()->route('configuration-cargo_types')->with('error','cargo code already in use');

        }else if($check->count() == 0){
            $check2=cargo_types::where('CargoName',$this->name);
            if($check2->count() > 0){
                return redirect()->route('configuration-cargo_types')->with('error','cargo name already in use');
            }else if($check2->count() ==0){

                $cargo=new cargo_types();
                $cargo->CargoCode=$this->code;
                $cargo->CargoName=$this->name;
                $cargo->CargoCategory=$this->category;
                $cargo->RatePerKgUsd=$this->rate_per_kg;
                $cargo->ChargeUsd=$this->charge_usd;
                $cargo->Taxable=$this->taxable;
                $cargo->specialCargo=$this->specialCargo;
                
                if($cargo->save()){
                    return redirect()->route('configuration-cargo_types')->with('success','cargo type added successfully');

                }else{
                    return redirect()->route('configuration-cargo_types')->with('error','Error: failed to add cargo type');
                }
                



            }
        }
    }

    public function editCargo(int $cargoid){

        $cargo=cargo_types::find($cargoid);
        if($cargo){
            $this->cargoid=$cargo->id;
            $this->name=$cargo->CargoName;
            $this->code=$cargo->CargoCode;
            $this->category=$cargo->CargoCategory;
            $this->specialCargo=$cargo->specialCargo;
            $this->charge_usd=$cargo->ChargeUsd;
            $this->taxable=$cargo->Taxable;
            $this->rate_per_kg=$cargo->RatePerKgUsd;


        }else{
            return redirect()->route('configuration-cargo_types')->with('error','Error, failed to get cargo type');
        }
    }

    public function updateCargo(){

        cargo_types::where('id',$this->cargoid)->update([
            'CargoName'=>$this->name,
            'CargoCode'=>$this->code,
            'CargoCategory'=>$this->category,
            'specialCargo'=>$this->specialCargo,
            'Taxable'=>$this->taxable,
            'ChargeUsd'=>$this->charge_usd,
            'RatePerKgUsd'=>$this->rate_per_kg

        ]);
        return redirect()->route('configuration-cargo_types')->with('success','Success, Cargo Type Updated Successfully');
    }

    public function resetInput(){

        $this->category='';
        $this->code='';
        $this->name='';
        $this->specialCargo='';
        $this->taxable='';
        $this->rate_per_kg='';
        $this->charge_usd='';

    }

    public function closeModal(){
        $this->resetInput();
    }

    public function render()
    {
        $cargo_types=cargo_types::all();
        return view('livewire.app.cargo-types-component',compact('cargo_types'))->layout('layouts.app');
    }
}
