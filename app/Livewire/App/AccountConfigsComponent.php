<?php

namespace App\Livewire\App;

use Livewire\Component;
use App\Models\accounts_configs;

class AccountConfigsComponent extends Component
{
    public $usdRate;
    public $Vat;
    public $poundRate;
    public $euroRate;
    public $forkLiftRate;

    public function insertRate(){
        $rate=new accounts_configs();
        $rate->Vat=$this->Vat;
        $rate->UsdRate=$this->usdRate;
        $rate->PoundRate=$this->poundRate;
        $rate->EuroRate=$this->euroRate;
        $rate->ForkliftHourlyRate=$this->forkLiftRate;
        $rate->CreatedBy=1;

        if($rate->save()){
            return redirect()->route('configuration-accounts_configs')->with('success','Rate updated successfully');

        }else{
           return redirect()->route('configuration-accounts_configs')->with('error','Error: Rate update failed'); 
        }
    }

    public function render()
    {
        $rates=accounts_configs::orderBy('created_at','DESC')->latest()->first();
        $logs=accounts_configs::orderBy('created_at','desc')->get();
        return view('livewire.app.account-configs-component',compact('rates','logs'))->layout('layouts.app');
    }
}
