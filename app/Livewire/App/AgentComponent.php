<?php

namespace App\Livewire\App;

use Livewire\Component;
use App\Models\agents;

class AgentComponent extends Component
{
    public $name;
    public $accountno;
    public $address1;
    public $address2;
    public $address3;
    public $email;
    public $accounttype;
    public $taxtype;
    public $agentphone;
    public $contactname;
    public $contactphone;
    public $contactfax;
    public $contactemail;
    public $paymenttype;


    public function addAgent(){
        
        $agent=new agents();
        $agent->AgentName=$this->name;
        $agent->AgentAccountNo=$this->accountno;
        $agent->AgentAddress1=$this->address1;
        $agent->AgentAddress2=$this->address2;
        $agent->AgentAddress3=$this->address3;
        $agent->AgentEmail=$this->email;
        $agent->AgentPhonenumber=$this->agentphone;
        $agent->AgentType=$this->accounttype;
        $agent->TaxType=$this->taxtype;
        $agent->ContactName=$this->contactname;
        $agent->ContactPhone=$this->contactphone;
        $agent->ContactFax=$this->contactfax;
        $agent->ContactEmail=$this->contactemail;
        $agent->PaymentType=$this->paymenttype;

        if($agent->save()){
            return redirect()->route('consignment_agents')->with('success','agent added successfully');

        }else{
             return redirect()->route('consignment_agents')->with('error','Error: Failed to add agent');
        }


    }
    public function render()
    {
        $agents=agents::all();
        return view('livewire.app.agent-component',compact('agents'))->layout('layouts.app');
    }
}
