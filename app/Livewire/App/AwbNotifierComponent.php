<?php

namespace App\Livewire\App;

use Livewire\Component;
use Illuminate\Support\Facades\DB;
use App\Models\awb;
use App\Models\awb_cargo_states;
use App\Models\awb_cargo_types;
use App\Models\awb_storage_types;
use App\Models\consignee;
use App\Models\manifests;
use App\Models\airlines;
use App\Models\airports;
use App\Models\manifest_consignee_allocation;
use App\Models\notifications_reminders_log;

class AwbNotifierComponent extends Component
{
    public $ManifestID;
    public $airlineCode;
    public $loadingpoint;
    public $unloadingpoint;

    public $awbid;
    public $ConsigneeID;
    public $ConsigneeName;
    public $Address1;
    public $Address2;
    public $Address3;
    public $consigneeEmail;
    public $contactName;
    public $contactEmail;
    public $contactPhone;

    public $Status;
    public $Feedback;
    public $Method;
    public $StartChargingDate;
    public $IsPartShipmentOf;

    public $firstmessage;
    public $firstMessageDate;

    public $reminderMessage;
    public $reminderDate;







    public function mount($id){
        $this->ManifestID=$id;
        $manifest=manifests::where('id',$this->ManifestID)->first();
        $this->airlineCode=$manifest->Airline;
        $this->loadingpoint=$manifest->PointOfLoading;
        $this->unloadingpoint=$manifest->PointOfUnloading;

}


public function viewConsignee(int $awb){

        $awb=awb::find($awb);
        if($awb){
            $this->awbid=$awb->id;
            $this->IsPartShipmentOf=$awb->IsPartShipmentOf;
            $cos_allo=manifest_consignee_allocation::where('AwbID',$this->awbid)->first();
            $this->ConsigneeID=$cos_allo->ConsigneeID;

            /////get consignee details/////
            $ConsigneeDetails=consignee::where('id',$this->ConsigneeID)->first();
            $this->ConsigneeName=$ConsigneeDetails->ConsigneeName;
            $this->Address1=$ConsigneeDetails->ConsigneeAddress1;
            $this->Address2=$ConsigneeDetails->ConsigneeAddress2;
            $this->Address3=$ConsigneeDetails->ConsigneeAddress3;
            $this->contactEmail=$ConsigneeDetails->ContactEmail;
            $this->contactName=$ConsigneeDetails->ContactName;
            $this->contactPhone=$ConsigneeDetails->ContactPhone;

            $check=notifications_reminders_log::where('AwbID',$this->awbid);
            if($check->count() == 0){

            }elseif($check->count() > 0){
                $notification=notifications_reminders_log::where('AwbID',$this->awbid)->where('NotificationOrReminder',1)->first();
                $this->firstMessageDate=$notification->created_at;
                $this->firstmessage=$notification->Feedback;

                $reminder=notifications_reminders_log::where('AwbID',$this->awbid)->where('NotificationOrReminder',2);
                if($reminder->count() == 0){

                }elseif($reminder->count() > 0){
                $reminder2=notifications_reminders_log::where('AwbID',$this->awbid)->where('NotificationOrReminder',2)->latest()->first();
                $this->reminderDate=$reminder2->created_at;
                $this->reminderMessage=$reminder2->Feedback;
                }
                

            }



        }else{
            //return redirect()->route('flight_manifests')->with('error','Error: Failed to retrieve manifest');
        }

    }

public function notifyConsignee(){
    //$NotifiedUserID=$this->ConsigneeID;
    $NotifierID=auth()->user()->id;
    $NotificationStatus=1;

    

        $notification=new notifications_reminders_log();
        $notification->AwbID=$this->awbid;
        $notification->NotifiedUserID=$this->ConsigneeID;
        $notification->NotifierID=$NotifierID;
        $notification->Method=$this->Method;
        $notification->Status=$this->Status;
        $notification->Feedback=$this->Feedback;
        $notification->NotificationOrReminder=$NotificationStatus;
        $notification->StartChargingDate=$this->StartChargingDate;
        awb::where('id',$this->awbid)->update([
            'NotificationStatus'=>$NotificationStatus,
        ]);
        if($notification->save()){
            if($this->IsPartShipmentOf!=='0'){
            $valid=1;
            $isFree=0;
            $generatePartshipmentNote = 0;
            $hasPartShipments = 1;

            $getawb=awb::where('id',$this->awbid)->first();
            $AwbNo=$getawb->AwbNo;
            $HAWB=$getawb->HAWB;

            awb::where('AwbNo',$AwbNo)->update([

                'hasPartShipments'=>$hasPartShipments,
            ]);
            awb::where('id',$this->awbid)->update([

                'generatePartshipmentNote'=>$generatePartshipmentNote,
            ]);

            ////////////MRA VALIDATE//////////////////
            $checkmra=awb::where('AwbNo',$AwbNo)->where('HAWB',$HAWB)->first();
            $releas=$checkmra->ReleaseStatus;
                if($releas==1){
                    $isFree=1;
                }else{

                }

                awb::where('id',$this->awbid)->update([
                    'MraValidated'=>$valid,
                    'isFree'=>$isFree,
                ]);


        }else{

            
        }
return redirect()->route('awb_notifier',[$this->ManifestID])->with('success',' Operation was Successful');
        }else{
            dd('am fucked');
        }

     

}


public function remindConsignee(){

    $NotifierID=auth()->user()->id;
    $NotificationStatus=2;

    

        $notification=new notifications_reminders_log();
        $notification->AwbID=$this->awbid;
        $notification->NotifiedUserID=$this->ConsigneeID;
        $notification->NotifierID=$NotifierID;
        $notification->Method=$this->Method;
        $notification->Status=$this->Status;
        $notification->Feedback=$this->Feedback;
        $notification->NotificationOrReminder=$NotificationStatus;
        $notification->StartChargingDate=$this->StartChargingDate;
        if($notification->save()){
            return redirect()->route('awb_notifier',[$this->ManifestID])->with('success',' Operation was Successful');

        }else{
            return redirect()->route('awb_notifier',[$this->ManifestID])->with('error',' Operation failed');
        }


}

public function closeModal(){


}


    public function render()
    {

            $awbs = DB::table('awbs')
                //->leftJoin('airlines', 'airlines.AirlineID', '=', 'manifests.Airline')
                //->leftJoin('airports', 'manifests.PointOfUnloading', '=', 'airports.AirportID')
                //->where('IsRoyalFlight', 'false')
                ->where('awbs.ManifestID', $this->ManifestID)
                ->orderBy('awbs.created_at', 'ASC')
                ->get();


    $Airline=airlines::where('id',$this->airlineCode)->first();
    $ManifestDetails=manifests::where('id',$this->ManifestID)->first();
    $loading=airports::where('id',$this->loadingpoint)->first();
    $unloading=airports::where('id',$this->unloadingpoint)->first();
    $Origins=airports::all();
    
    $Destinations=airports::where('AirportCode','LLW')->OrWhere('AirportCode','BLZ')->get();

        return view('livewire.app.awb-notifier-component',compact('ManifestDetails','loading','unloading','Airline','Origins','Destinations','awbs'))
        ->layout('layouts.app');
    }
}
