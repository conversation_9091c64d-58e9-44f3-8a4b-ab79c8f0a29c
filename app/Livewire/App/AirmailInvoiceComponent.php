<?php

namespace App\Livewire\App;

use Livewire\Component;
use App\Models\manifests;
use App\Models\awb;
use App\Models\cargo_types;
use App\Models\accounts_configs;

class AirmailInvoiceComponent extends Component
{


    public $manifestId;
    public $airlineName = '-';
    public $flightNo = '-';
    public $flightDate = '-';
    public $status = '-';
    public $pointOfLoading = '-';
    public $pointOfUnloading = '-';
    public $royalFlightWeight = 0;
    
    public $usdRate = 0;
    public $vat = 0;
    
    public $ratePerKgUsd = 0;
    public $chargeUsd = 0;
    
    public $totalPieces = 0;
    public $totalWeight = 0;
    public $totalCharges = 0;
    public $vatTotal = 0;
    public $mainTotal = 0;
    
    public function mount($id)
    {
        $this->manifestId = $id;
        $this->loadData();
    }
    protected function loadData()
    {
        // Load manifest data
        $manifest = manifests::where('id', $this->manifestId)->first();
            
        if ($manifest) {
            //$this->airlineName = $manifest->airline->AirlineName ?? '-';
            $this->flightNo = $manifest->FlightNo ?? '-';
            $this->flightDate = $manifest->FlightDate ?? '-';
            //$this->status = $manifest->Status ?? '-';
            //$this->pointOfLoading = $manifest->loadingAirport->AirportName ?? '-';
            //$this->pointOfUnloading = $manifest->unloadingAirport->AirportName ?? '-';
            //$this->royalFlightWeight = $manifest->RoyalFlightWeight ?? 0;
        }
        
        // Load accounts config
        $accountsConfig = accounts_configs::latest('created_at')->first();
        if ($accountsConfig) {
            $this->usdRate = $accountsConfig->UsdRate;
            $this->vat = $accountsConfig->Vat;
        }
        
        // Load cargo type data
        $cargoType = cargo_types::where('CargoCode', 'POA')->first();
        if ($cargoType) {
            $this->ratePerKgUsd = $cargoType->RatePerKgUsd;
            $this->chargeUsd = $cargoType->ChargeUsd;
        }
        
        // Calculate AWB data
        $awbs = awb::where('ManifestID', $this->manifestId)
            ->where('IsPostOfficeCargo', 1)
            ->get();
            
        $this->totalPieces = $awbs->sum('TotalPieces');
        $this->totalWeight = $awbs->sum('ChargableWgt');
        
        $figureToShow = (floatval($this->ratePerKgUsd) * floatval($this->totalWeight));
        if ($figureToShow <= $this->chargeUsd) {
            $figureToShow = $this->chargeUsd;
        }
        
        $this->totalCharges = $figureToShow * $this->usdRate;
        $this->vatTotal = $this->vat * $this->totalCharges;
        $this->mainTotal = $this->vatTotal + $this->totalCharges;
    }

     // Helper method to format currency
     public function formatCurrency($value)
     {
         return number_format($value, 2);
     }

    public function render()
    {
        
        return view('livewire.app.airmail-invoice-component')->layout('layouts.app');
    }
}
