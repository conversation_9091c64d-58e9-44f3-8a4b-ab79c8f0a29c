<?php

namespace App\Livewire\App;

use Livewire\Component;
use App\Models\airlines;

class AirlinesComponent extends Component
{
    public $prefix;
    public $name;
    public $code;
    public $frequency;

    public function addAirline(){
        $verify=airlines::where('AirlinePrefix',$this->prefix);
       
        

        if($verify->count() > 0){
            return redirect()->route('configuration-airlines')->with('error','prefix already in use');

        }else if($verify->count() == 0){
             $verify2=airlines::where('AirlineName',$this->name);
             if($verify2->count() > 0){
                return redirect()->route('configuration-airlines')->with('error','airline name already exist');

             }else if($verify2->count()==0){
              $verify3=airlines::where('AirlineCode',$this->code); 
              if($verify3->count() > 0){
                return redirect()->route('configuration-airlines')->with('error','airline code already exist');
              } else if($verify3->count() == 0){
                $line=new airlines();
                $line->AirlineName=$this->name;
                $line->AirlineCode=$this->code;
                $line->AirlineFrequency=$this->frequency;
                $line->AirlinePrefix=$this->prefix;

                if($line->save()){
                    return redirect()->route('configuration-airlines')->with('success','airline created successfully');
                }else{
                    return redirect()->route('configuration-airlines')->with('error','Error: failed to create airline');
                }

              }

             }

        }



    }
    public function render()
    {
        $airlines=airlines::all();
        return view('livewire.app.airlines-component',compact('airlines'))->layout('layouts.app');
    }
}
