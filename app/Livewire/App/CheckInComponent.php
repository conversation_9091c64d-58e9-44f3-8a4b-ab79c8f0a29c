<?php

namespace App\Livewire\App;

use Livewire\Component;
use App\Models\awb;
use App\Models\cargo_state;
use App\Models\consignee;
use App\Models\awb_cargo_states;
use App\Models\manifest_consignee_allocation;

class CheckInComponent extends Component
{
    public $ManifestID;
    public $AwbShow;
    public $HawbShow;
    public $OnHandPieces;
    public $OnHandWgtVol;
    public $ToCome;
    public $IsCompletelyMissing=0;

    public $CargoState0;
    public $CargoState0Pieces;
    public $CargoState0Weight;
    public $Comment;

    public $CargoState1;
    public $CargoState1Pieces;
    public $CargoState1Weight;
    public $cargoState1Remarks;

    public $CargoState2;
    public $CargoState2Pieces;
    public $CargoState2Weight;
    public $cargoState2Remarks;

    public $CargoState3;
    public $CargoState3Pieces;
    public $CargoState3Weight;
    public $cargoState3Remarks;

    public $IsAirMail;
    public $AirportBranch;

    public $awbID;
    

    public function mount($id){
        $this->ManifestID=$id;


    }

public function CheckInGoods(int $awbID){
            $awb=awb::find($awbID);
            $this->awbID=$awb->id;
            $this->AwbShow=$awb->AwbNo;
            $this->HawbShow=$awb->HAWB;
            $this->OnHandPieces=$awb->TotalPieces;
            $this->OnHandWgtVol=$awb->TotalGrossWgtVol;
        

    }

    public function updateCheckInGoods(){
        $serialNumber="";
        $CheckedInStatus = 1;
        $CreatedBy= 1;
        $Date=date('Y-m-d H:i:s');
        $CheckInUser=Auth()->user()->id;
        //get details
        $awBDetails=awb::where('id',$this->awbID)->first();
        $ispartShipmentOf=$awBDetails->ispartShipmentOf;
         $IsConsolidated = $awBDetails->IsConsolidated;
         $serialNumber = "";
         $IsPartShipmentOf = $awBDetails->IsPartShipmentOf;
         $DocValidationNo=substr( strtoupper(md5($serialNumber)) ,0,16);
        $CargoMissing=0;
        //completly missing

        if($this->IsCompletelyMissing=='true'){
            $CargoMissing=1;
            $TotalPieces = $this->OnHandPieces;
            $TotalWeight = $this->OnHandWgtVol;
            
        }else{

            $this->IsCompletelyMissing=0;

        }

        //IsOfficeCargo
        if($awBDetails->IsPostOfficeCargo==1){

            $IsAllocated=manifest_consignee_allocation::where('AwbID',$this->awbID);
            if($IsAllocated->count() > 0){
                //dd($this->awbID);

            }else{
                $selectConsignee=consignee::where('ConsigneeAccountNo','MPT100')->first();
                $ConsigneeID=$selectConsignee->id;

                $selectAgent=consignee::where('id',0)->first();
                $AgentID=$selectAgent->id;

                $allocate=new manifest_consignee_allocation();
                $allocate->AwbID=$this->awbID;
                $allocate->ConsigneeID=$ConsigneeID;
                $allocate->AgentID=$AgentID;
                $allocate->ChargeType=0;
                $allocate->ChargeAmount='0';
                $allocate->CreatedBy=1;
                $allocate->CurrencyID=0;
                $allocate->save();


            }
        
        }
//endIsOfficeCargo

//partOfShipment
    if($IsPartShipmentOf!=0){

        $selectMainAwb=awb::where('AwbNo',$IsPartShipmentOf);
        if($selectMainAwb->count() > 0){
            $selectMainAwbID=awb::where('AwbNo',$IsPartShipmentOf)->first();
            $mainAwbID  = $selectMainAwbID->id;

            $selectConsignee=manifest_consignee_allocation::where('AwbID',$mainAwbID);
            if($selectConsignee->count() > 0){
                //manifest_consignee_allocation::where('AwbID',$mainAwbID)->delete();

            }elseif($selectConsignee->count() == 0){
                


            }

            

        }elseif($selectMainAwb->count() == 0){
            $selectMainAwb=awb::where('HAWB',$IsPartShipmentOf);
            if($selectMainAwb->count() > 0){

            }elseif($selectMainAwb->count() == 0){

            }
            
        }
        

}

    //update awb_cargo_states
    awb_cargo_states::where('AwbID',$this->awbID)->update([

            'CargoState0' =>$this->CargoState0,
            'CargoState0Pieces' =>$this->CargoState0Pieces, 
            'CargoState0Weight' =>$this->CargoState0Weight, 

            'CargoState1' =>$this->CargoState1, 
            'CargoState1Pieces' =>$this->CargoState1Pieces, 
            'CargoState1Weight' =>$this->CargoState1Weight, 

            'CargoState2' =>$this->CargoState2, 
            'CargoState2Pieces' =>$this->CargoState2Pieces, 
            'CargoState2Weight' =>$this->CargoState2Weight, 

            'CargoState3' =>$this->CargoState3,  
            'CargoState3Pieces' =>$this->CargoState3Pieces, 
            'CargoState3Weight' =>$this->CargoState3Weight,

    ]);


        awb::where('id',$this->awbID)->update([
            
            'SerialNo' => $serialNumber, 
            'OnHandPieces' =>$this->OnHandPieces,
            'OnHandWgtVol' =>$this->OnHandWgtVol, 
            'ToCome' =>$this->ToCome, 
            'Comment' =>$this->Comment,
            'CheckedInStatus' =>$CheckedInStatus,
            'CheckedInDate' =>$Date,
            'CargoState1Remarks' =>$this->cargoState1Remarks,
            'CargoState2Remarks' =>$this->cargoState2Remarks ,
            'CargoState3Remarks' =>$this->cargoState3Remarks ,
            'IsCompletelyMissing' =>$this->IsCompletelyMissing ,
            'CheckInUser' =>$CheckInUser ,
            'DocValidationNo' => $DocValidationNo,

        ]);
        
    



return redirect()->route('check_in_goods',[$this->ManifestID])->with('success','Checkin Successfully');


}

    
    public function resetInput(){

            $this->awbID='';
            $this->AwbShow='';
            $this->HawbShow='';
            $this->OnHandPieces='';
            $this->OnHandWgtVol='';

    }

    public function closeModal(){
        $this->resetInput();
    }
    public function render()
    {
        $cargoStates=cargo_state::all();
        $awbs=awb::where('ManifestID',$this->ManifestID)
        ->where('IsConsolidated',0)
        ->where('IsExcess',0)
        //->where('CheckedInStatus',0)
        ->where('isDeleted',0)
        ->get();
        return view('livewire.app.check-in-component',compact('awbs','cargoStates'))->layout('layouts.app');
    }
}
