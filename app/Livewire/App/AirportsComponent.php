<?php

namespace App\Livewire\App;

use Livewire\Component;
use App\Models\airports;
use App\Models\countries;

class AirportsComponent extends Component
{
    public $name;
    public $code;
    public $country;
    public $frequency;
    public $airportid;

    public function addAirport(){

        $check=airports::where('AirportCode',$this->code);
        if($check->count() > 0){
            return redirect()->route('configuration-airports')->with('error','code already in use');

        }else if($check->count() ==0){
            $check2=airports::where('AirportName',$this->name);
            if($check2->count() > 0){
                return redirect()->route('configuration-airports')->with('error','name already in use');
            }else if($check2->count() ==0){
                $airport=new airports();
                $airport->AirportCode=$this->code;
                $airport->AirportName=$this->name;
                $airport->AirportCountry=$this->country;
                $airport->Frequency=$this->frequency;

                if($airport->save()){
                    return redirect()->route('configuration-airports')->with('success','new airport added successfully');
                }else{
                    return redirect()->route('configuration-airports')->with('error','Error. failed to add airport');
                }

            } 

        }
    }

    public function editAirport(int $airportid){

        $airport=airports::find($airportid);
        if($airport){
            $this->airportid=$airport->id;
            $this->name=$airport->AirportName;
            $this->code=$airport->AirportCode;
            $this->frequency=$airport->Frequency;
            $this->country=$airport->AirportCountry;


        }else{
            return redirect()->route('configuration-airports')->with('error','Error, failed to get airport');
        }
    }

    public function updateAirport(){
        airports::where('id',$this->airportid)->update([
            'AirportName'=>$this->name,
            'AirportCode'=>$this->code,
            'AirportCountry'=>$this->country,
            'Frequency'=>$this->frequency
        ]);


        return redirect()->route('configuration-airports')->with('success','Success, airport updated successfully');

    }

    public function resetInput(){

        $this->code='';
        $this->name='';
        $this->country='';
        $this->frequency='';
    }

    public function closeModal(){
        $this->resetInput();
    }

    public function render()
    {
        $airports=airports::all();
        $countries=countries::all();
        return view('livewire.app.airports-component',compact('airports','countries'))->layout('layouts.app');
    }
}
