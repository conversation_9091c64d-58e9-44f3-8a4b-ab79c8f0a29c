<?php

namespace App\Livewire\App;

use Livewire\Component;
use Illuminate\Support\Facades\DB;
//use App\Models\manifests;
//use App\Models\awb;


class AirmailComponent extends Component
{
    public $airportCode;
    public function render()
    {
        $cargotypecheckin=1;
        $this->airportCode=auth()->user()->branch;
        $awbs = DB::table('awbs')
            ->selectRaw('SUM(TotalPieces) as TotalPieces, SUM(TotalGrossWgtVol) as TotalGrossWgtVol, manifests.FlightNo, manifests.FlightDate, manifests.id')
            ->leftJoin('manifests', 'manifests.id', '=', 'awbs.ManifestID')
            ->where('IsPostOfficeCargo', 1)
            ->where('CheckoutStatus', 0)
            ->where('PointOfDest', $this->airportCode)
            ->where('CheckedInStatus', 1)
            ->where('CargoTypeCheckIn', $cargotypecheckin)
           ->groupBy('manifests.FlightNo', 'manifests.FlightDate', 'manifests.id')
           // ->orderByDesc('manifests.FlightDate')
            ->get();
        return view('livewire.app.airmail-component',compact('awbs'))->layout('layouts.app');
    }
}
