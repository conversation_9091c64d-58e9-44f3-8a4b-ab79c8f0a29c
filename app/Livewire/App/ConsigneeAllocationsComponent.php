<?php

namespace App\Livewire\App;

use Livewire\Component;
use App\Models\awb;
use App\Models\awb_cargo_states;
use App\Models\awb_cargo_types;
use App\Models\awb_storage_types;
use App\Models\consignee;
use App\Models\manifests;
use App\Models\airlines;
use App\Models\airports;


class ConsigneeAllocationsComponent extends Component
{
    public $ManifestID;
    public $airlineCode;
    public $loadingpoint;
    public $unloadingpoint;

    public function mount($id){
        $this->ManifestID=$id;
        $manifest=manifests::where('id',$this->ManifestID)->first();
        $this->airlineCode=$manifest->Airline;
        $this->loadingpoint=$manifest->PointOfLoading;
        $this->unloadingpoint=$manifest->PointOfUnloading;

    }


    public function render()
    {
        $awbs=awb::where('ManifestID',$this->ManifestID)
        ->where('IsConsolidated',0)
        ->where('IsPostOfficeCargo',0)
        //->orwhere('IsPostOfficeCargo',1)
        ->where('IsCompletelyMissing',0)
        ->where('isExcess',0)
        ->get();

        


    $Airline=airlines::where('id',$this->airlineCode)->first();
    $ManifestDetails=manifests::where('id',$this->ManifestID)->first();
    $loading=airports::where('id',$this->loadingpoint)->first();
    $unloading=airports::where('id',$this->unloadingpoint)->first();
    $Origins=airports::all();
    
    $Destinations=airports::where('AirportCode','LLW')->OrWhere('AirportCode','BLZ')->get();
        return view('livewire.app.consignee-allocations-component',compact(
            'awbs','ManifestDetails','loading','unloading','Airline','Origins','Destinations'))
        ->layout('layouts.app');
    }
}
