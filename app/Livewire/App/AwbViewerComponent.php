<?php

namespace App\Livewire\App;

use Livewire\Component;
use App\Models\awb;
use App\Models\airlines;
use App\Models\airports;
use App\Models\manifests;
use App\Models\awb_cargo_states;
use App\Models\awb_cargo_types;
use App\Models\awb_charges;
use App\Models\awb_storage_types;
use Livewire\WithFileUploads;
use App\Models\xml_awb;
use SimpleXMLElement;
use XMLReader;

class AwbViewerComponent extends Component
{   use WithFileUploads;

    public $ManifestID;
    public $airlineCode;
    public $loadingpoint;
    public $unloadingpoint;


    public $AwbNo;
    public $TotalPieces;
    public $TotalGrossWgtVol;
    public $CargoDescription;
    public $PointOfOrg;
    public $PointOfDest;
    public $NatureOfGoods;
    public $IsConsolidated;
    public $IsPartShipmentMain;
    public $IsPostOfficeCargo;
    public $DocValidationNo="-";

    public $AwB_HAWB;
    public $HawbNo;
    public $AwbID;
    public $awbid;

    public $remarks;
    public $excessAwbNo;

    /////////////////////////////////////////////////////////////////////////////
    public $PartShipmentHAwbNo;
    public $PartShipmentAwbNo;
    public $PartShipmentTotalPieces;
    public $PartShipmentTotalGrossWgtVol;
    public $PartShipmentCargoDescription;
    public $PartShipmentPointOfOrg;
    public $PartShipmentPointOfDest;
    public $PartShipmentNatureOfGoods;

    public $xmlFile;
    // Validation rules
    protected $rules = [
        'xmlFile' => 'required|file|mimes:xml|max:1024', // Allow only XML files up to 1MB
    ];

    public function mount($id){

        $this->ManifestID=$id;
        $manifest=manifests::where('id',$this->ManifestID)->first();
        $this->airlineCode=$manifest->Airline;
        $this->loadingpoint=$manifest->PointOfLoading;
        $this->unloadingpoint=$manifest->PointOfUnloading;

        $AwB1=awb::where('ManifestID',$this->ManifestID);
        if($AwB1->count() > 0){
            $AwB=awb::where('ManifestID',$this->ManifestID)->first();
            $this->AwB_HAWB=$AwB->AwbNo;
            $this->AwbID=$AwB->id;
        }elseif($AwB1->count() ==0){


        }
        

    }

    public function insertAwb(){

        $isExcess = 0;
        $SerialNo="-";
        $this->IsPostOfficeCargo=0;
        $verify=awb::where('AwbNo',$this->AwbNo);
        if($verify->count() > 0){
            return redirect()->route('awb_viewer',[$this->ManifestID])->with('error','Duplicate AwbNo:'.$this->AwbNo);
        }elseif($verify->count()==0){
            $awb=new awb();
            $awb->SerialNo=$SerialNo;
            $awb->AwbNo=$this->AwbNo;
            $awb->ManifestID=$this->ManifestID;
            $awb->TotalPieces=$this->TotalPieces;
            $awb->TotalGrossWgtVol=$this->TotalGrossWgtVol;
            $awb->CargoDescription=$this->CargoDescription;
            $awb->PointOfOrg=$this->PointOfOrg;
            $awb->PointOfDest=$this->PointOfDest;
            $awb->NatureOfGoods=$this->NatureOfGoods;
            $awb->DocValidationNo=$this->DocValidationNo;
            $awb->IsConsolidated=$this->IsConsolidated;
            $awb->IsPostOfficeCargo=$this->IsPostOfficeCargo;
            $awb->IsPartShipmentMain=$this->IsPartShipmentMain;
            $awb->isExcess=$isExcess;
            
            if($awb->save()){
                $new=awb::orderBy('created_at','DESC')->latest()->limit(1)->first();
                    $AwBID=$new->id;

                $awb_cargo_states=new awb_cargo_states();
                $awb_cargo_states->AwbID=$AwBID;
                $awb_cargo_states->save();

                $awb_storage_types=new awb_storage_types();
                $awb_storage_types->AwbID=$AwBID;
                $awb_storage_types->save();

                $awb_cargo_types=new awb_cargo_types();
                $awb_cargo_types->AwbID=$AwBID;
                $awb_cargo_types->save();

                $awb_charges=new awb_charges();
                $awb_charges->AwbID=$AwBID;
                $awb_charges->save();

            return redirect()->route('awb_viewer',$this->ManifestID)->with('success','Success.AWB Added Successfully');

            }
                    
                


        }

    }

    public function editAwb(int $awbid){
        $this->AwbID=$awbid;
        $awb=awb::find($awbid);
        if($awb){
            $this->awbid=$awb->id;
            $this->AwbNo=$awb->AwbNo;
            $this->TotalPieces=$awb->TotalPieces;
            $this->TotalGrossWgtVol=$awb->TotalGrossWgtVol;
            $this->CargoDescription=$awb->CargoDescription;
            $this->PointOfOrg=$awb->PointOfOrg;
            $this->PointOfDest=$awb->PointOfDest;
            $this->NatureOfGoods=$awb->NatureOfGoods;
            $this->IsConsolidated=$awb->IsConsolidated;
            $this->IsPartShipmentMain=$awb->IsPartShipmentMain;

        }else{
            return redirect()->route('awb_viewer',[$this->ManifestID])->with('error','Error: Failed to get AwB'); 
        }

}

public function updateAwb(){

        awb::where('id',$this->awbid)->update([
            'AwbNo'=>$this->AwbNo,
            'TotalPieces'=>$this->TotalPieces,
            'TotalGrossWgtVol'=>$this->TotalGrossWgtVol,
            'CargoDescription'=>$this->CargoDescription,
            'PointOfOrg'=>$this->PointOfOrg,
            'PointOfDest'=>$this->PointOfDest,
            'NatureOfGoods'=>$this->NatureOfGoods,
            'IsConsolidated'=>$this->IsConsolidated,
            'IsPartShipmentMain'=>$this->IsPartShipmentMain,

        ]);

        return redirect()->route('awb_viewer',[$this->ManifestID])->with('success','Success: AwB Updated Successfully');
    }

    public function insertHAWB(){
            $isExcess = 0;
            $this->IsConsolidated=0;
            $this->IsPostOfficeCargo=0;
            $IsPartOfConsolidatedAwbID=0;
            $verify=awb::where('HAWB',$this->HawbNo);
            if($verify->count()>0){
                return redirect()->route('awb_viewer',[$this->ManifestID])->with('error','Duplicate HAWB No.:'.$this->HawbNo);
            }else if($verify->count() ==0){
            $awb=new awb();
            $awb->HAWB=$this->HawbNo;
            $awb->AwbNo=$this->AwB_HAWB;
            $awb->ManifestID=$this->ManifestID;
            $awb->TotalPieces=$this->TotalPieces;
            $awb->TotalGrossWgtVol=$this->TotalGrossWgtVol;
            $awb->CargoDescription=$this->CargoDescription;
            $awb->PointOfOrg=$this->PointOfOrg;
            $awb->PointOfDest=$this->PointOfDest;
            $awb->NatureOfGoods=$this->NatureOfGoods;
            $awb->DocValidationNo=$this->DocValidationNo;
            $awb->IsConsolidated=$this->IsConsolidated;
            $awb->IsPartOfConsolidatedAwbID=$IsPartOfConsolidatedAwbID;
            $awb->IsPostOfficeCargo=$this->IsPostOfficeCargo;
            $awb->IsPartShipmentMain=$this->IsPartShipmentMain;
            $awb->isExcess=$isExcess;

                if($awb->save()){

                $new=awb::orderBy('created_at','DESC')->latest()->limit(1)->first();
                    $AwBID=$new->id;

                $awb_cargo_states=new awb_cargo_states();
                $awb_cargo_states->AwbID=$AwBID;
                $awb_cargo_states->save();

                $awb_storage_types=new awb_storage_types();
                $awb_storage_types->AwbID=$AwBID;
                $awb_storage_types->save();

                $awb_cargo_types=new awb_cargo_types();
                $awb_cargo_types->AwbID=$AwBID;
                $awb_cargo_types->save();

                $awb_charges=new awb_charges();
                $awb_charges->AwbID=$AwBID;
                $awb_charges->save();
                    return redirect()->route('awb_viewer',$this->ManifestID)->with('success','Success.Operation was Successfully');
                }else{
                    return redirect()->route('awb_viewer',[$this->ManifestID])->with('error','Error: Failed to process request');   
                }

            }

    }


 public function insertPostalMail(){
            $isExcess = 0;
            $this->IsConsolidated=0;
            $this->IsPostOfficeCargo=1;
            $this->IsPartShipmentMain=0;
            $this->NatureOfGoods='Mail';
            $this->CargoDescription='Air Mail';
            $IsPartOfConsolidatedAwbID=0;
            $verify=awb::where('AwbNo',$this->AwbNo);
            if($verify->count()>0){
                return redirect()->route('awb_viewer',[$this->ManifestID])->with('error','Duplicate AwbNo:'.$this->AwbNo);
            }else if($verify->count() ==0){
            $awb=new awb();
            $awb->AwbNo=$this->AwbNo;
            $awb->ManifestID=$this->ManifestID;
            $awb->TotalPieces=$this->TotalPieces;
            $awb->TotalGrossWgtVol=$this->TotalGrossWgtVol;
            $awb->CargoDescription=$this->CargoDescription;
            $awb->PointOfOrg=$this->PointOfOrg;
            $awb->PointOfDest=$this->PointOfDest;
            $awb->NatureOfGoods=$this->NatureOfGoods;
            $awb->DocValidationNo=$this->DocValidationNo;
            $awb->IsConsolidated=$this->IsConsolidated;
            $awb->IsPartOfConsolidatedAwbID=$IsPartOfConsolidatedAwbID;
            $awb->IsPostOfficeCargo=$this->IsPostOfficeCargo;
            $awb->IsPartShipmentMain=$this->IsPartShipmentMain;
            $awb->isExcess=$isExcess;

                if($awb->save()){

                    return redirect()->route('awb_viewer',$this->ManifestID)->with('success','Success.Operation was Successfully');
                }else{
                    return redirect()->route('awb_viewer',[$this->ManifestID])->with('error','Error: Failed to process request');   
                }

            }

 }

 public function insertExcessDocuments(){

    $isExcess=1;
    $search=awb::where('AwbNo',$this->excessAwbNo);
    if($search->count() > 0){

        return redirect()->route('awb_viewer',[$this->ManifestID])->with('error','Duplicate AwbNo:'.$this->AwbNo);

    }else{

        try {
            $excess=new awb();
            $excess->AwbNo=$this->excessAwbNo;
            $excess->ManifestID=$this->ManifestID;
            $excess->isExcess=$isExcess;
            $excess->Comment=$this->remarks;
            $excess->save();
            return redirect()->route('awb_viewer',[$this->ManifestID])->with('success','Excess Document Was Successful');
            
        } catch (\Exception $e) {
            return redirect()->route('awb_viewer',[$this->ManifestID])->with('error','Operation  failed');
            
        }

        

    }
 }





 //////////////////////upload awb xml ///////////////////////////////////////////
 public function uploadAWB(){
   
    if($this->xmlFile!==''){
        $reader = new XMLReader();
    $reader->open($this->xmlFile->getRealPath());
    while ($reader->read()) {
        if ($reader->nodeType == XMLReader::ELEMENT && $reader->name == 'item') {
            $item = new SimpleXMLElement($reader->readOuterXML());
            xml_awb::create([
                'AwbNo'=>$item->AwbNo,
                'HAWB'=>$item->HAWB,
                'PointOfOrg'=>$item->PointOfOrg,
                'PointOfDest'=>$item->PointOfDest,
                'TotalPieces'=>$item->TotalPieces,
                'NatureOfGoods'=>$item->NatureOfGoods,
                'TotalGrossWgtVol'=>$item->TotalGrossWgtVol,
                'Comment'=>$item->Comment,
                'CreatedBy'=>auth()->user()->id,
                'ManifestID'=>$this->ManifestID,
              ]);     
        
        }
    }

    }else{
        return redirect()->route('flight_manifests')->with('error','file empty');
    }
$reader->close();
return redirect()->route('flight_manifests')->with('success','Success: Manifest uploaded successfully');

}


 public function deleteWaybill(){

    $isDeleted=1;

    try {
        awb::where('id',$this->awbid)->where('ManifestID',$this->ManifestID)->update([
            'isDeleted'=>$isDeleted,

        ]);
        return redirect()->route('awb_viewer',[$this->ManifestID])->with('success','Deleted Successfuly');
    } catch (\Exception $e) {
        return redirect()->route('awb_viewer',[$this->ManifestID])->with('error','Operation  failed');
    }
 }


 public function handlePartShipment(){

        $AwbNo=$this->PartShipmentAwbNo;
        $IsConsolidated=0;
        $hawb = $this->PartShipmentHAwbNo;
        $AwbID=0;
        $isExcess = 0;

        if($hawb == ''){
            $hawb = '-';
        }

        $searchAwbNo=awb::where('AwbNo',$this->PartShipmentAwbNo);
        if($searchAwbNo->count() == 0){
            return redirect()->route('awb_viewer',[$this->ManifestID])->with('error','Invalid AwbNo  Provided');
        }elseif($searchAwbNo->count() > 0){

            $searchHAWB=awb::where('HAWB',$this->PartShipmentHAwbNo);
            if($searchHAWB->count() > 0){
                return redirect()->route('awb_viewer',[$this->ManifestID])->with('error','Duplicate HAWB No. Provided');
            }elseif($searchHAWB->count() == 0){

                $getAwbNo=awb::where('AwbNo',$AwbNo)->first();
            $AwbID=$getAwbNo->id;
            $releaseStatus=$getAwbNo->ReleaseStatus;
            $partOfMain = 1;
            $generatePartshipmentNote=0;
            if($releaseStatus == 1){
                $generatePartshipmentNote = 1;
                $partOfMain = 0;
            } else {
                awb::where('id',$AwbID)->update([
                    'hasPartShipments'=>1,
                ]);
                
            }

            }


            
        }

        if($hawb!='-'){
                try {
                    $awb=new awb();
                    $awb->AwbNo=$this->PartShipmentAwbNo;
                    $awb->HAWB=$this->PartShipmentHAwbNo;
                    $awb->ManifestID=$this->ManifestID;
                    $awb->TotalPieces=$this->PartShipmentTotalPieces;
                    $awb->TotalGrossWgtVol=$this->PartShipmentTotalGrossWgtVol;
                    $awb->CargoDescription=$this->PartShipmentCargoDescription;
                    $awb->PointOfOrg=$this->PartShipmentPointOfOrg;
                    $awb->PointOfDest=$this->PartShipmentPointOfDest;
                    $awb->NatureOfGoods=$this->PartShipmentNatureOfGoods;
                    $awb->DocValidationNo=$this->DocValidationNo;
                    $awb->IsConsolidated=$IsConsolidated;
                    $awb->IsPartShipmentOf=$this->PartShipmentAwbNo;
                    $awb->partOfMain=$partOfMain;
                    $awb->generatePartshipmentNote=$generatePartshipmentNote;
                    $awb->isExcess=$isExcess;
                    $awb->save();
                    return redirect()->route('awb_viewer',[$this->ManifestID])->with('success','Part Shipment Added Successfuly');
                    
                } catch (Exception $e) {
                    return redirect()->route('awb_viewer',[$this->ManifestID])->with('error','Part Shipment failed');
                }

        }else{
            try {
                    $awb=new awb();
                    $awb->AwbNo=$this->PartShipmentAwbNo;
                    $awb->ManifestID=$this->ManifestID;
                    $awb->TotalPieces=$this->PartShipmentTotalPieces;
                    $awb->TotalGrossWgtVol=$this->PartShipmentTotalGrossWgtVol;
                    $awb->CargoDescription=$this->PartShipmentCargoDescription;
                    $awb->PointOfOrg=$this->PartShipmentPointOfOrg;
                    $awb->PointOfDest=$this->PartShipmentPointOfDest;
                    $awb->NatureOfGoods=$this->PartShipmentNatureOfGoods;
                    $awb->DocValidationNo=$this->DocValidationNo;
                    $awb->IsConsolidated=$IsConsolidated;
                    $awb->IsPartShipmentOf=$this->PartShipmentAwbNo;
                    $awb->partOfMain=$partOfMain;
                    $awb->generatePartshipmentNote=$generatePartshipmentNote;
                    $awb->isExcess=$isExcess;
                    $awb->save();
                    return redirect()->route('awb_viewer',[$this->ManifestID])->with('success','Part Shipment Added Successfuly');
                    
                } catch (Exception $e) {
                    return redirect()->route('awb_viewer',[$this->ManifestID])->with('error','Part Shipment failed');
                }
            try {
                

                $awb_cargo_states=new awb_cargo_states();
                $awb_cargo_states->AwbID=$AwbID;
                $awb_cargo_states->save();

                $awb_storage_types=new awb_storage_types();
                $awb_storage_types->AwbID=$AwbID;
                $awb_storage_types->save();

                $awb_cargo_types=new awb_cargo_types();
                $awb_cargo_types->AwbID=$AwbID;
                $awb_cargo_types->save();

                $awb_charges=new awb_charges();
                $awb_charges->AwbID=$AwbID;
                $awb_charges->save();
                
            } catch (Exception $e) {
                
            }

        }

        

        


 }


    public function resetInput(){

            $this->AwbNo='';
            $this->TotalPieces='';
            $this->TotalGrossWgtVol='';
            $this->CargoDescription='';
            $this->PointOfOrg='';
            $this->PointOfDest='';
            $this->NatureOfGoods='';
            $this->IsConsolidated='';
            $this->IsPartShipmentMain='';

    }

    public function closeModal(){
        $this->resetInput();
    }

    public function render()
    {
    $awbs=awb::where('ManifestID',$this->ManifestID)
    ->where('IsPartOfConsolidatedAwbID',0)
    ->where('isDeleted',0)
    ->orderBy('created_at','DESC')->get();

    $Airline=airlines::where('id',$this->airlineCode)->first();
    $ManifestDetails=manifests::where('id',$this->ManifestID)->first();
    $loading=airports::where('id',$this->loadingpoint)->first();
    $unloading=airports::where('id',$this->unloadingpoint)->first();
    $Origins=airports::all();
    $Destinations=airports::where('AirportCode','LLW')->OrWhere('AirportCode','BLZ')->get();

        return view('livewire.app.awb-viewer-component',compact(
            'awbs','ManifestDetails','loading','unloading','Airline','Origins','Destinations'
        ))->layout('layouts.app');
    }
}
