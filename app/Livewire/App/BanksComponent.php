<?php

namespace App\Livewire\App;

use Livewire\Component;
use App\Models\banks;
use App\Models\countries;

class BanksComponent extends Component
{
    public $name;
    public $acc_number;
    public $country_code;
    public $swift_bic;
    public $bankid;


    public function addBank(){
        $check=banks::where('AccountNo',$this->acc_number);
        if($check->count() > 0){
            return redirect()->route('configuration-banks')->with('error','AccountNo already in use');

        }else if($check->count() == 0){
            $check2=banks::where('BankSwiftBIC',$this->swift_bic);
            if($check2->count() > 0){
                return redirect()->route('configuration-banks')->with('error','Swift Code already in use');
            }else if($check2->count() ==0){

                

                $cd=countries::where('Code',$this->country_code);
                if($cd->count() > 0){
                    $bank=new banks();
                    $bank->AccountNo=$this->acc_number;
                    $bank->BankSwiftBIC=$this->swift_bic;
                    $bank->BankName=$this->name;
                    $bank->BankCountryCode=$this->country_code;
                    $bank->save();
                    return redirect()->route('configuration-banks')->with('success','Bank addedd successfully');

                }else if($cd->count() ==0){
                    return redirect()->route('configuration-banks')->with('error','Wrong Country Code provided');

                }

            }

        }
    }

    public function editBank(int $bankid){

        $bank=banks::find($bankid);
        if($bank){
            $this->bankid=$bank->id;
            $this->name=$bank->BankName;
            $this->acc_number=$bank->AccountNo;
            $this->swift_bic=$bank->BankSwiftBIC;
            $this->country_code=$bank->BankCountryCode;


        }else{
            return redirect()->route('configuration-banks')->with('error','failed to get bank');
        }

    }


    public function updateBank(){

        banks::where('id',$this->bankid)->update([
            'BankName'=>$this->name,
            'AccountNo'=>$this->acc_number,
            'BankSwiftBIC'=>$this->swift_bic,
            'BankCountryCode'=>$this->country_code,

        ]);

        return redirect()->route('configuration-banks')->with('success','Bank updated successfully');
    }


    public function resetInput(){

            $this->name='';
            $this->acc_number='';
            $this->swift_bic='';
            $this->country_code='';

    }

    public function closeModal(){
        $this->resetInput();
    }
    public function render()
    {
        $banks=banks::all();
        $countries=countries::all();
        return view('livewire.app.banks-component',compact('banks','countries'))->layout('layouts.app');
    }
}
