<?php

namespace App\Livewire\App;

use Livewire\Component;
use App\Models\awb;
use App\Models\awb_cargo_states;
use App\Models\awb_cargo_types;
use App\Models\awb_storage_types;
use App\Models\consignee;
use App\Models\manifests;
use App\Models\airlines;
use App\Models\airports;
use App\Models\manifest_consignee_allocation;
use App\Models\currencies;
use App\Models\cargo_types;
use App\Models\storage_types;
use Livewire\Carbon;

class ConsigneeCargoAllocationsComponent extends Component
{
    public $ManifestID;
    public $airlineid;
    public $loadingpoint;
    public $unloadingpoint;
    public $AwBID;
    public $AwbNo;
    public $HawbNo;

    public $ALConsignee;
    public $ALAgent;
    public $ALCurrency;
    public $ALChargeType;
    public $CChargeType="";

    public $ConsigneeName;
    public $AgentName;
    public $CurrencyName;

    public $ChargeType;
    public $ChargeAmount;
    public $ChargableWgt;
    public $ConsigneeID;
    public $AgentID;
    public $CurrencyID;

    ////////////////////////////////////////////////
    public $CargoType1;
    public $CargoType1Weight;

    public $CargoType2;
    public $CargoType2Weight;

    public $CargoType3;
    public $CargoType3Weight;

    public $CargoType4;
    public $CargoType4Weight;

    public $StorageType1;
    public $StorageType1Weight;

    public $StorageType2;
    public $StorageType2Weight;


    public $StorageType1WeightStorage;
    public $StorageType2WeightStorage;
    public $StorageType1Storage;
    public $StorageType2Storage;

    /////////////////////////////////////////////////



    public function mount($id){
        $this->AwBID=$id;
        $g=awb::where('id',$this->AwBID)->first();
        $this->ManifestID=$g->ManifestID;
        $this->ChargableWgt=$g->ChargableWgt;
        $this->AwbNo=$g->AwbNo;
        $this->HawbNo=$g->HAWB;

        $airL=manifests::where('id',$this->ManifestID)->first();
        $this->airlineid=$airL->Airline;
        $this->loadingpoint=$airL->PointOfLoading;
        $this->unloadingpoint=$airL->PointOfUnloading;

       // $Consignee=consignee::where('id',$this->ALConsignee)->first();
       // $Agent=consignee::where('id',$this->ALAgent)->first();
        //$Currency=currencies::where('id',$this->ALCurrency)->first();

        $g2=manifest_consignee_allocation::where('AwbID',$this->AwBID);
            if($g2->count()>0){
                $CAl=manifest_consignee_allocation::where('AwbID',$this->AwBID)->first();
                    $this->ALConsignee=$CAl->ConsigneeID;
                    $this->ALAgent=$CAl->AgentID;
                    $this->ALCurrency=$CAl->CurrencyID;
                    $this->ALChargeType=$CAl->ChargeType;
                    $this->ChargeAmount=$CAl->ChargeAmount;

                    $Consignee=consignee::where('id',$this->ALConsignee)->first();
                    $Agent=consignee::where('id',$this->ALAgent)->first();

                    $C=currencies::where('id',$this->ALCurrency);
                    if($C->count() > 0){
                        $Currency=currencies::where('id',$this->ALCurrency)->first();
                        $this->ConsigneeName=$Consignee->ConsigneeName;
                        $this->AgentName=$Agent->ConsigneeName;
                        $this->CurrencyName=$Currency->Currency;

                        $this->ALChargeType=$CAl->ChargeType;
                        if($this->ALChargeType==1){
                            $this->CChargeType="CHARGE COLLECT (CC)";

                        }else{
                            $this->CChargeType="PREPAID (PP)";  
                        }

                    }elseif($C->count() ==0){

                    }
                        

            }elseif($g2->count()==0){
                //$this->CChargeType="PREPAID (PP)"; 
                
            }

                    


        

    }

    public function consineeAllocation(){

        $CurrencyID=1;
        $ChargableWgt=0;
        $EmptyConsigneeOwner="";
        if($CurrencyID!='' && $ChargableWgt!=''){
            $CurrencyID=$this->CurrencyID;
            $ChargableWgt=$this->ChargableWgt;  

        }else{

        }

        try {

            awb::where('id',$this->AwBID)->update([
                'ChargableWgt'=>$this->ChargableWgt,
                
            ]);

           // Clear Agents
           consignee::where('id',$this->ConsigneeID)->update([
                'consignee_owner'=>$EmptyConsigneeOwner,
            ]);

            // Asign Agents to consignee
            consignee::where('id',$this->ConsigneeID)->update([
                'consignee_owner'=>$this->AgentID,
            ]);

            

        } catch (\Exception $e) {
            
        }
        manifest_consignee_allocation::where('AwbID',$this->AwBID)->delete();

            $CreatedBy=1;
            $new=new manifest_consignee_allocation();
            $new->AwbID=$this->AwBID;
            $new->ConsigneeID=$this->ConsigneeID;
            $new->AgentID=$this->AgentID;
            $new->ChargeType=$this->ChargeType;
            $new->ChargeAmount=$this->ChargeAmount;
            $new->CreatedBy=$CreatedBy;
            $new->CurrencyID=$this->CurrencyID;

            if($new->save()){
                return redirect()->route('consignee_cargo_allocation',[$this->AwBID])->with('success','Operation Was Successful');
            }else{
                return redirect()->route('consignee_cargo_allocation',[$this->AwBID])->with('error','Operation Failed');
            }
      

}


public function cargoTypeCheckin(){
    $CheckInUser=auth()->user()->id;
    $CargoTypeCheckIn=1;
    $Date=date('Y-m-d H:i:s');
    //update cargo storage typea
        try {

            awb_cargo_types::where('AwbID',$this->AwBID)->update([
                'CargoType1'=>$this->CargoType1,
                'CargoType1Weight'=>$this->CargoType1Weight,
                'CargoType2'=>$this->CargoType2,
                'CargoType2Weight'=>$this->CargoType2Weight,
                'CargoType3'=>$this->CargoType3,
                'CargoType3Weight'=>$this->CargoType3Weight,
                'CargoType4'=>$this->CargoType4,
                'CargoType4Weight'=>$this->CargoType4Weight,
                

            ]);
        

    
    } catch (Exception $e) {
        
    }

    // Update Storage Types

    try {
        awb_storage_types::where('AwbID',$this->AwBID)->update([
            'StorageType1'=>$this->StorageType1,
            'StorageType1Weight'=>$this->StorageType1Weight,
            'StorageType2'=>$this->StorageType2,
            'StorageType2Weight'=>$this->StorageType2Weight,
            

        ]);
        
    } catch (Exception $e) {
        
    }

    ///update awb
    try {
        awb::where('id',$this->AwBID)->update([
            'CargoTypeCheckIn'=>$CargoTypeCheckIn,
            'CargoTypeCheckInUser'=>$CheckInUser,
            'ChargableWgt'=>$this->ChargableWgt,
            'cargoTypeCheckInDate'=>$Date,
        ]);
         
    } catch (Exception $e) {
        
    }

    return redirect()->route('consignee_cargo_allocation',[$this->AwBID])->with('success','Operation Was Successful');
    
}

public function storageTypeCheckInGoods(){
    $CheckInUser=auth()->user()->id;
    $CargoTypeCheckIn=1;
    $Date=date('Y-m-d H:i:s');


    try {
        awb_storage_types::where('AwbID',$this->AwBID)->update([
            'StorageType1'=>$this->StorageType1Storage,
            'StorageType1Weight'=>$this->StorageType1WeightStorage,
            'StorageType2'=>$this->StorageType2Storage,
            'StorageType2Weight'=>$this->StorageType2WeightStorage,
            'Timestamp'=>$Date,

        ]);
        
    } catch (Exception $e) {
        
    }


    try {
        awb::where('id',$this->AwBID)->update([
            'CargoTypeCheckIn'=>$CargoTypeCheckIn,
            'CargoTypeCheckInUser'=>$CheckInUser,
        ]);
    
    } catch (Exception $e) {
        
    }

    return redirect()->route('consignee_cargo_allocation',[$this->AwBID])->with('success','Operation Was Successful');
}


public function resetInput(){

    $this->StorageType1Weight='';
    $this->StorageType2Weight='';
    $this->StorageType1='';
    $this->StorageType2='';
    $this->CargoType1='';
    $this->CargoType2='';
    $this->CargoType3='';
    $this->CargoType4='';
    $this->CargoType1Weight='';
    $this->CargoType2Weight='';
    $this->CargoType3Weight='';
    $this->CargoType4Weight='';

    $this->StorageType1WeightStorage='';
    $this->StorageType2WeightStorage='';
    $this->StorageType1Storage='';
    $this->StorageType2Storage='';

}

public function closeModal(){
$this->resetInput();
}


    public function render()
    {
    $Airline=airlines::where('id',$this->airlineid)->first();
    $ManifestDetails=manifests::where('id',$this->ManifestID)->first();
    $loading=airports::where('id',$this->loadingpoint)->first();
    $unloading=airports::where('id',$this->unloadingpoint)->first();
    $awb=awb::where('id',$this->AwBID)->first();
    $cargotypes=cargo_types::orderBy('id','ASC')->get();
    $storagetypes=storage_types::orderBy('id','ASC')->get();
    
    $currencies=currencies::orderBy('id','ASC')->get();
    $consignees=consignee::where('AccountType',1)->get();
    $agents=consignee::where('AccountType',2)->get();
    $Destinations=airports::where('AirportCode','LLW')->OrWhere('AirportCode','BLZ')->get();
        return view('livewire.app.consignee-cargo-allocations-component',compact(
            'ManifestDetails','loading','unloading','Airline','Destinations',
            'consignees','agents','currencies','awb','cargotypes','storagetypes'
            ))->layout('layouts.app');
    }
}
